import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:scholara_student/core/enums/homework/assignment_type.dart';
import 'package:scholara_student/core/enums/homework/homework_status.dart';
import 'package:scholara_student/core/enums/homework/submission_type.dart';
import 'package:scholara_student/features/homework/controllers/homework_filter_controller.dart';
import 'package:scholara_student/features/homework/models/homework_model.dart';

/// Test suite for assignment type filtering functionality
void main() {
  group('Assignment Type Filtering Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('HomeworkFilterType Extension Tests', () {
      test('should return correct labels for all filter types', () {
        expect(HomeworkFilterType.all.label, 'All');
        expect(HomeworkFilterType.classAssignments.label, 'Class Assignments');
        expect(HomeworkFilterType.individual.label, 'Individual');
        expect(HomeworkFilterType.group.label, 'Group');
      });

      test('should return correct short labels for all filter types', () {
        expect(HomeworkFilterType.all.shortLabel, 'All');
        expect(HomeworkFilterType.classAssignments.shortLabel, 'Class');
        expect(HomeworkFilterType.individual.shortLabel, 'Individual');
        expect(HomeworkFilterType.group.shortLabel, 'Group');
      });
    });

    group('HomeworkFilterNotifier Tests', () {
      test('should initialize with "all" filter', () {
        final state = container.read(homeworkFilterProvider);

        expect(state, HomeworkFilterType.all);
      });

      test('should update filter type correctly', () {
        final notifier = container.read(homeworkFilterProvider.notifier);

        notifier.setFilter(HomeworkFilterType.individual);
        expect(
          container.read(homeworkFilterProvider),
          HomeworkFilterType.individual,
        );

        notifier.setFilter(HomeworkFilterType.group);
        expect(
          container.read(homeworkFilterProvider),
          HomeworkFilterType.group,
        );

        notifier.setFilter(HomeworkFilterType.classAssignments);
        expect(
          container.read(homeworkFilterProvider),
          HomeworkFilterType.classAssignments,
        );
      });

      test('should reset to "all" filter', () {
        final notifier = container.read(homeworkFilterProvider.notifier);

        // Set to a different filter first
        notifier.setFilter(HomeworkFilterType.individual);
        expect(
          container.read(homeworkFilterProvider),
          HomeworkFilterType.individual,
        );

        // Reset should go back to "all"
        notifier.reset();
        expect(container.read(homeworkFilterProvider), HomeworkFilterType.all);
      });
    });

    group('ClassFilterNotifier Tests', () {
      test('should initialize with null class filter', () {
        final state = container.read(classFilterProvider);
        expect(state, isNull);
      });

      test('should update class filter correctly', () {
        final notifier = container.read(classFilterProvider.notifier);

        notifier.setClass('class_001');
        expect(container.read(classFilterProvider), 'class_001');

        notifier.setClass('class_002');
        expect(container.read(classFilterProvider), 'class_002');
      });

      test('should clear class filter', () {
        final notifier = container.read(classFilterProvider.notifier);

        // Set a class first
        notifier.setClass('class_001');
        expect(container.read(classFilterProvider), 'class_001');

        // Clear should set to null
        notifier.clearClass();
        expect(container.read(classFilterProvider), isNull);
      });
    });

    group('Homework Model Assignment Type Tests', () {
      test('should correctly identify class assignments', () {
        final homework = _createTestHomework(
          AssignmentType.classAssignment,
          classId: 'test_class_001',
        );

        expect(homework.isClassAssignment, isTrue);
        expect(homework.isIndividualAssignment, isFalse);
        expect(homework.isGroupAssignment, isFalse);
      });

      test('should correctly identify individual assignments', () {
        final homework = _createTestHomework(AssignmentType.individual);

        expect(homework.isClassAssignment, isFalse);
        expect(homework.isIndividualAssignment, isTrue);
        expect(homework.isGroupAssignment, isFalse);
      });

      test('should correctly identify group assignments', () {
        final homework = _createTestHomework(AssignmentType.group);

        expect(homework.isClassAssignment, isFalse);
        expect(homework.isIndividualAssignment, isFalse);
        expect(homework.isGroupAssignment, isTrue);
      });

      test('should correctly check user assignment for class assignments', () {
        final homework = _createTestHomework(
          AssignmentType.classAssignment,
          classId: 'test_class_001',
        );

        // Class assignments should return true for any user (assuming they're in the class)
        expect(homework.isAssignedToUser('any_user_id'), isTrue);
      });

      test(
        'should correctly check user assignment for individual assignments',
        () {
          final homework = _createTestHomework(
            AssignmentType.individual,
            assignedUserIds: ['user_001', 'user_002'],
          );

          expect(homework.isAssignedToUser('user_001'), isTrue);
          expect(homework.isAssignedToUser('user_002'), isTrue);
          expect(homework.isAssignedToUser('user_003'), isFalse);
        },
      );

      test('should correctly check user assignment for group assignments', () {
        final homework = _createTestHomework(
          AssignmentType.group,
          assignedUserIds: ['user_001', 'user_002', 'user_003'],
        );

        expect(homework.isAssignedToUser('user_001'), isTrue);
        expect(homework.isAssignedToUser('user_002'), isTrue);
        expect(homework.isAssignedToUser('user_003'), isTrue);
        expect(homework.isAssignedToUser('user_004'), isFalse);
      });
    });

    group('Assignment Type Extension Tests', () {
      test('should return correct properties for class assignment type', () {
        const type = AssignmentType.classAssignment;

        expect(type.label, 'Class Assignment');
        expect(type.shortLabel, 'Class');
        expect(type.requiresClassId, isTrue);
        expect(type.requiresAssignedUserIds, isFalse);
        expect(type.description, 'Assigned to all students in a class');
      });

      test(
        'should return correct properties for individual assignment type',
        () {
          const type = AssignmentType.individual;

          expect(type.label, 'Individual Assignment');
          expect(type.shortLabel, 'Individual');
          expect(type.requiresClassId, isFalse);
          expect(type.requiresAssignedUserIds, isTrue);
          expect(type.description, 'Assigned to specific individual students');
        },
      );

      test('should return correct properties for group assignment type', () {
        const type = AssignmentType.group;

        expect(type.label, 'Group Assignment');
        expect(type.shortLabel, 'Group');
        expect(type.requiresClassId, isFalse);
        expect(type.requiresAssignedUserIds, isTrue);
        expect(type.description, 'Assigned to a group of students');
      });

      test('should return correct properties for custom assignment type', () {
        const type = AssignmentType.custom;

        expect(type.label, 'Custom Assignment');
        expect(type.shortLabel, 'Custom');
        expect(type.requiresClassId, isFalse);
        expect(type.requiresAssignedUserIds, isTrue);
        expect(type.description, 'Custom assignment configuration');
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle empty assigned user IDs list', () {
        final homework = _createTestHomework(
          AssignmentType.individual,
          assignedUserIds: [],
        );

        expect(homework.assignedUserIds, isEmpty);
        expect(homework.isAssignedToUser('any_user'), isFalse);
      });

      test('should handle null class ID for class assignments', () {
        final homework = _createTestHomework(
          AssignmentType.classAssignment,
          classId: null,
        );

        expect(homework.classId, isNull);
        expect(homework.isClassAssignment, isTrue);
      });

      test('should handle assignment type changes in copyWith', () {
        final originalHomework = _createTestHomework(
          AssignmentType.classAssignment,
          classId: 'test_class_001',
        );

        final updatedHomework = originalHomework.copyWith(
          assignmentType: AssignmentType.individual,
          assignedUserIds: ['user_001'],
        );

        expect(updatedHomework.assignmentType, AssignmentType.individual);
        expect(updatedHomework.assignedUserIds, ['user_001']);
        expect(updatedHomework.isIndividualAssignment, isTrue);
      });
    });
  });
}

/// Helper function to create test homework with specified assignment type
HomeworkModel _createTestHomework(
  AssignmentType assignmentType, {
  String? classId,
  List<String>? assignedUserIds,
}) {
  return HomeworkModel(
    id: 'test_homework_001',
    subject: 'Test Subject',
    title: 'Test Homework',
    description: 'Test description',
    assignedAt: DateTime.now().subtract(const Duration(days: 1)),
    dueAt: DateTime.now().add(const Duration(days: 7)),
    requiresSubmission: true,
    submissionType: SubmissionType.online,
    status: HomeworkStatus.pending,
    resourceUrls: [],
    classId: classId,
    teacherId: 'test_teacher_001',
    assignmentType: assignmentType,
    assignedUserIds: assignedUserIds ?? [],
  );
}
