import 'package:flutter_test/flutter_test.dart';

import 'package:scholara_student/core/enums/homework/assignment_type.dart';
import 'package:scholara_student/core/enums/homework/homework_status.dart';
import 'package:scholara_student/core/enums/homework/submission_type.dart';
import 'package:scholara_student/features/homework/models/homework_model.dart';

/// Test suite for backward compatibility with existing homework data
void main() {
  group('Backward Compatibility Tests', () {
    group('Legacy Homework Data Parsing', () {
      test('should parse legacy homework JSON without assignment type fields', () {
        // Simulate legacy homework data (without assignmentType and assignedUserIds)
        final legacyHomeworkJson = {
          'id': 'legacy_hw_001',
          'subject': 'Mathematics',
          'title': 'Legacy Math Homework',
          'description': 'This is legacy homework data',
          'assignedAt': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
          'dueAt': DateTime.now().add(const Duration(days: 5)).toIso8601String(),
          'requiresSubmission': true,
          'submissionType': 'online',
          'status': 'pending',
          'resourceUrls': ['https://example.com/resource.pdf'],
          'teacherNote': 'Complete all exercises',
          'classId': 'class_math_001',
          'teacherId': 'teacher_001',
          'submissionId': null,
          // Note: assignmentType and assignedUserIds are missing (legacy data)
        };

        // Should parse successfully with default values
        final homework = HomeworkModel.fromJson(legacyHomeworkJson);

        expect(homework.id, 'legacy_hw_001');
        expect(homework.subject, 'Mathematics');
        expect(homework.title, 'Legacy Math Homework');
        expect(homework.assignmentType, AssignmentType.classAssignment); // Default value
        expect(homework.assignedUserIds, isEmpty); // Default empty list
        expect(homework.classId, 'class_math_001');
        expect(homework.isClassAssignment, isTrue);
      });

      test('should parse homework JSON with null assignment type', () {
        final homeworkJsonWithNullType = {
          'id': 'hw_null_type',
          'subject': 'Science',
          'title': 'Homework with Null Type',
          'assignedAt': DateTime.now().toIso8601String(),
          'requiresSubmission': false,
          'submissionType': 'offline',
          'status': 'done',
          'resourceUrls': <String>[],
          'assignmentType': null, // Explicitly null
          'assignedUserIds': null, // Explicitly null
        };

        final homework = HomeworkModel.fromJson(homeworkJsonWithNullType);

        expect(homework.assignmentType, AssignmentType.classAssignment); // Default fallback
        expect(homework.assignedUserIds, isEmpty); // Default empty list
      });

      test('should parse homework JSON with invalid assignment type', () {
        final homeworkJsonWithInvalidType = {
          'id': 'hw_invalid_type',
          'subject': 'English',
          'title': 'Homework with Invalid Type',
          'assignedAt': DateTime.now().toIso8601String(),
          'requiresSubmission': true,
          'submissionType': 'online',
          'status': 'pending',
          'resourceUrls': <String>[],
          'assignmentType': 'invalid_type', // Invalid enum value
          'assignedUserIds': ['student_001'],
        };

        final homework = HomeworkModel.fromJson(homeworkJsonWithInvalidType);

        expect(homework.assignmentType, AssignmentType.classAssignment); // Fallback to default
        expect(homework.assignedUserIds, ['student_001']); // Should still parse correctly
      });
    });

    group('Legacy Data Migration Simulation', () {
      test('should handle migration from legacy to new format', () {
        // Simulate a batch of legacy homework data
        final legacyHomeworkList = [
          {
            'id': 'legacy_001',
            'subject': 'Mathematics',
            'title': 'Algebra Homework',
            'assignedAt': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
            'requiresSubmission': true,
            'submissionType': 'online',
            'status': 'pending',
            'resourceUrls': <String>[],
            'classId': 'class_math_001',
            'teacherId': 'teacher_001',
          },
          {
            'id': 'legacy_002',
            'subject': 'Science',
            'title': 'Chemistry Lab',
            'assignedAt': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
            'requiresSubmission': false,
            'submissionType': 'offline',
            'status': 'done',
            'resourceUrls': <String>[],
            'classId': 'class_science_001',
            'teacherId': 'teacher_002',
          },
        ];

        // Parse all legacy homework
        final parsedHomework = legacyHomeworkList
            .map((json) => HomeworkModel.fromJson(json))
            .toList();

        // Verify all homework was parsed correctly with defaults
        expect(parsedHomework.length, 2);
        
        for (final homework in parsedHomework) {
          expect(homework.assignmentType, AssignmentType.classAssignment);
          expect(homework.assignedUserIds, isEmpty);
          expect(homework.isClassAssignment, isTrue);
          expect(homework.classId, isNotNull);
        }
      });

      test('should maintain existing functionality after migration', () {
        final legacyHomework = HomeworkModel.fromJson({
          'id': 'legacy_functional_test',
          'subject': 'History',
          'title': 'World War II Essay',
          'description': 'Write a 1000-word essay',
          'assignedAt': DateTime.now().subtract(const Duration(days: 3)).toIso8601String(),
          'dueAt': DateTime.now().add(const Duration(days: 4)).toIso8601String(),
          'requiresSubmission': true,
          'submissionType': 'online',
          'status': 'pending',
          'resourceUrls': ['https://example.com/essay_guidelines.pdf'],
          'teacherNote': 'Focus on causes and effects',
          'classId': 'class_history_001',
          'teacherId': 'teacher_003',
        });

        // Test that all existing functionality still works
        expect(legacyHomework.subject, 'History');
        expect(legacyHomework.title, 'World War II Essay');
        expect(legacyHomework.requiresSubmission, isTrue);
        expect(legacyHomework.status, HomeworkStatus.pending);
        expect(legacyHomework.submissionType, SubmissionType.online);
        expect(legacyHomework.resourceUrls.length, 1);
        expect(legacyHomework.teacherNote, 'Focus on causes and effects');
        
        // Test new functionality with defaults
        expect(legacyHomework.assignmentType, AssignmentType.classAssignment);
        expect(legacyHomework.isClassAssignment, isTrue);
        expect(legacyHomework.isAssignedToUser('any_user'), isTrue); // Class assignments are visible to all
      });
    });

    group('JSON Serialization Backward Compatibility', () {
      test('should serialize new homework to include all fields', () {
        final newHomework = HomeworkModel(
          id: 'new_hw_001',
          subject: 'Mathematics',
          title: 'New Math Homework',
          assignedAt: DateTime.now(),
          requiresSubmission: true,
          submissionType: SubmissionType.online,
          status: HomeworkStatus.pending,
          resourceUrls: [],
          assignmentType: AssignmentType.individual,
          assignedUserIds: ['student_001', 'student_002'],
        );

        final json = newHomework.toJson();

        expect(json['assignmentType'], 'individual');
        expect(json['assignedUserIds'], ['student_001', 'student_002']);
        expect(json.containsKey('assignmentType'), isTrue);
        expect(json.containsKey('assignedUserIds'), isTrue);
      });

      test('should serialize legacy homework with default values', () {
        final legacyHomework = HomeworkModel(
          id: 'legacy_serialization_test',
          subject: 'Science',
          title: 'Legacy Science Homework',
          assignedAt: DateTime.now(),
          requiresSubmission: false,
          submissionType: SubmissionType.offline,
          status: HomeworkStatus.done,
          resourceUrls: [],
          classId: 'class_science_001',
          // Using default values for new fields
        );

        final json = legacyHomework.toJson();

        expect(json['assignmentType'], 'classAssignment'); // Default value
        expect(json['assignedUserIds'], isEmpty); // Default empty list
        expect(json['classId'], 'class_science_001');
      });
    });

    group('Equality and Comparison Backward Compatibility', () {
      test('should maintain equality comparison with new fields', () {
        final homework1 = HomeworkModel(
          id: 'equality_test_001',
          subject: 'Mathematics',
          title: 'Test Homework',
          assignedAt: DateTime(2024, 1, 1),
          requiresSubmission: true,
          submissionType: SubmissionType.online,
          status: HomeworkStatus.pending,
          resourceUrls: [],
          assignmentType: AssignmentType.classAssignment,
          assignedUserIds: [],
        );

        final homework2 = HomeworkModel(
          id: 'equality_test_001',
          subject: 'Mathematics',
          title: 'Test Homework',
          assignedAt: DateTime(2024, 1, 1),
          requiresSubmission: true,
          submissionType: SubmissionType.online,
          status: HomeworkStatus.pending,
          resourceUrls: [],
          assignmentType: AssignmentType.classAssignment,
          assignedUserIds: [],
        );

        expect(homework1, equals(homework2));
        expect(homework1.hashCode, equals(homework2.hashCode));
      });

      test('should detect differences in new fields', () {
        final homework1 = HomeworkModel(
          id: 'difference_test_001',
          subject: 'Mathematics',
          title: 'Test Homework',
          assignedAt: DateTime(2024, 1, 1),
          requiresSubmission: true,
          submissionType: SubmissionType.online,
          status: HomeworkStatus.pending,
          resourceUrls: [],
          assignmentType: AssignmentType.classAssignment,
          assignedUserIds: [],
        );

        final homework2 = homework1.copyWith(
          assignmentType: AssignmentType.individual,
          assignedUserIds: ['student_001'],
        );

        expect(homework1, isNot(equals(homework2)));
        expect(homework1.hashCode, isNot(equals(homework2.hashCode)));
      });
    });

    group('CopyWith Backward Compatibility', () {
      test('should handle copyWith without specifying new fields', () {
        final originalHomework = HomeworkModel.fromJson({
          'id': 'copywith_test',
          'subject': 'Original Subject',
          'title': 'Original Title',
          'assignedAt': DateTime.now().toIso8601String(),
          'requiresSubmission': true,
          'submissionType': 'online',
          'status': 'pending',
          'resourceUrls': <String>[],
        });

        final updatedHomework = originalHomework.copyWith(
          title: 'Updated Title',
          status: HomeworkStatus.done,
        );

        expect(updatedHomework.title, 'Updated Title');
        expect(updatedHomework.status, HomeworkStatus.done);
        expect(updatedHomework.subject, 'Original Subject'); // Unchanged
        expect(updatedHomework.assignmentType, AssignmentType.classAssignment); // Default preserved
        expect(updatedHomework.assignedUserIds, isEmpty); // Default preserved
      });

      test('should handle copyWith with new fields specified', () {
        final originalHomework = HomeworkModel.fromJson({
          'id': 'copywith_new_fields_test',
          'subject': 'Test Subject',
          'title': 'Test Title',
          'assignedAt': DateTime.now().toIso8601String(),
          'requiresSubmission': true,
          'submissionType': 'online',
          'status': 'pending',
          'resourceUrls': <String>[],
        });

        final updatedHomework = originalHomework.copyWith(
          assignmentType: AssignmentType.group,
          assignedUserIds: ['student_001', 'student_002', 'student_003'],
        );

        expect(updatedHomework.assignmentType, AssignmentType.group);
        expect(updatedHomework.assignedUserIds, ['student_001', 'student_002', 'student_003']);
        expect(updatedHomework.isGroupAssignment, isTrue);
        expect(updatedHomework.subject, 'Test Subject'); // Unchanged
      });
    });
  });
}
