import 'package:flutter_test/flutter_test.dart';

import 'package:scholara_student/core/enums/homework/assignment_type.dart';
import 'package:scholara_student/core/enums/homework/homework_status.dart';
import 'package:scholara_student/core/enums/homework/submission_type.dart';
import 'package:scholara_student/features/classroom/models/class_model.dart';
import 'package:scholara_student/features/classroom/enums/classroom_type.dart';
import 'package:scholara_student/features/homework/models/homework_model.dart';

/// Test suite for class-specific filtering functionality
void main() {
  group('Class Filtering Tests', () {
    late List<HomeworkModel> testHomeworkList;
    late List<ClassModel> testClassList;

    setUp(() {
      // Create test classes
      testClassList = [
        _createTestClass('class_math_001', 'Mathematics 10A', 'Mathematics'),
        _createTestClass('class_science_001', 'Science 10B', 'Science'),
        _createTestClass('class_english_001', 'English 10C', 'English'),
      ];

      // Create test homework for different classes
      testHomeworkList = [
        // Math class homework
        _createTestHomework(
          'hw_math_001',
          'Mathematics',
          'Algebra Practice',
          AssignmentType.classAssignment,
          classId: 'class_math_001',
        ),
        _createTestHomework(
          'hw_math_002',
          'Mathematics',
          'Geometry Problems',
          AssignmentType.classAssignment,
          classId: 'class_math_001',
        ),

        // Science class homework
        _createTestHomework(
          'hw_science_001',
          'Science',
          'Chemistry Lab Report',
          AssignmentType.classAssignment,
          classId: 'class_science_001',
        ),

        // English class homework
        _createTestHomework(
          'hw_english_001',
          'English',
          'Essay Writing',
          AssignmentType.classAssignment,
          classId: 'class_english_001',
        ),

        // Individual assignments (no class)
        _createTestHomework(
          'hw_individual_001',
          'Mathematics',
          'Personal Math Tutoring',
          AssignmentType.individual,
          assignedUserIds: ['student_001'],
        ),

        // Group assignments (no class)
        _createTestHomework(
          'hw_group_001',
          'Science',
          'Group Research Project',
          AssignmentType.group,
          assignedUserIds: ['student_001', 'student_002', 'student_003'],
        ),
      ];
    });

    group('Class-based Homework Filtering', () {
      test('should filter homework by specific class ID', () {
        final mathHomework = testHomeworkList
            .where((hw) => hw.classId == 'class_math_001')
            .toList();

        expect(mathHomework.length, 2);
        expect(mathHomework.every((hw) => hw.subject == 'Mathematics'), isTrue);
        expect(
          mathHomework.every((hw) => hw.classId == 'class_math_001'),
          isTrue,
        );
      });

      test('should return empty list for non-existent class ID', () {
        final nonExistentClassHomework = testHomeworkList
            .where((hw) => hw.classId == 'class_nonexistent')
            .toList();

        expect(nonExistentClassHomework, isEmpty);
      });

      test('should filter homework for multiple classes', () {
        final mathAndScienceHomework = testHomeworkList
            .where(
              (hw) =>
                  hw.classId == 'class_math_001' ||
                  hw.classId == 'class_science_001',
            )
            .toList();

        expect(mathAndScienceHomework.length, 3);
        expect(
          mathAndScienceHomework.any((hw) => hw.subject == 'Mathematics'),
          isTrue,
        );
        expect(
          mathAndScienceHomework.any((hw) => hw.subject == 'Science'),
          isTrue,
        );
      });

      test(
        'should exclude individual and group assignments when filtering by class',
        () {
          final classOnlyHomework = testHomeworkList
              .where((hw) => hw.classId != null)
              .toList();

          expect(classOnlyHomework.length, 4);
          expect(classOnlyHomework.every((hw) => hw.isClassAssignment), isTrue);
          expect(classOnlyHomework.every((hw) => hw.classId != null), isTrue);
        },
      );
    });

    group('Assignment Type and Class Relationship', () {
      test('should verify class assignments have class IDs', () {
        final classAssignments = testHomeworkList
            .where((hw) => hw.assignmentType == AssignmentType.classAssignment)
            .toList();

        expect(classAssignments.length, 4);
        expect(classAssignments.every((hw) => hw.classId != null), isTrue);
      });

      test(
        'should verify individual assignments typically have no class ID',
        () {
          final individualAssignments = testHomeworkList
              .where((hw) => hw.assignmentType == AssignmentType.individual)
              .toList();

          expect(individualAssignments.length, 1);
          expect(
            individualAssignments.every((hw) => hw.classId == null),
            isTrue,
          );
        },
      );

      test('should verify group assignments typically have no class ID', () {
        final groupAssignments = testHomeworkList
            .where((hw) => hw.assignmentType == AssignmentType.group)
            .toList();

        expect(groupAssignments.length, 1);
        expect(groupAssignments.every((hw) => hw.classId == null), isTrue);
      });
    });

    group('Class Information Integration', () {
      test('should match homework class IDs with actual classes', () {
        final classIds = testClassList.map((c) => c.id).toSet();
        final homeworkClassIds = testHomeworkList
            .where((hw) => hw.classId != null)
            .map((hw) => hw.classId!)
            .toSet();

        // All homework class IDs should exist in the class list
        expect(homeworkClassIds.every((id) => classIds.contains(id)), isTrue);
      });

      test('should group homework by class name', () {
        final homeworkByClass = <String, List<HomeworkModel>>{};

        for (final homework in testHomeworkList) {
          if (homework.classId != null) {
            final classModel = testClassList.firstWhere(
              (c) => c.id == homework.classId,
            );
            final className = classModel.name;

            homeworkByClass[className] = (homeworkByClass[className] ?? [])
              ..add(homework);
          }
        }

        expect(homeworkByClass.keys.length, 3);
        expect(homeworkByClass['Mathematics 10A']?.length, 2);
        expect(homeworkByClass['Science 10B']?.length, 1);
        expect(homeworkByClass['English 10C']?.length, 1);
      });

      test('should get class subject from homework class ID', () {
        final mathHomework = testHomeworkList.firstWhere(
          (hw) => hw.classId == 'class_math_001',
        );

        final mathClass = testClassList.firstWhere(
          (c) => c.id == mathHomework.classId,
        );

        expect(mathClass.subject, 'Mathematics');
        expect(mathHomework.subject, 'Mathematics');
      });
    });

    group('Complex Filtering Scenarios', () {
      test('should filter by class and assignment status', () {
        final pendingMathHomework = testHomeworkList
            .where(
              (hw) =>
                  hw.classId == 'class_math_001' &&
                  hw.status == HomeworkStatus.pending,
            )
            .toList();

        // All test homework is created with pending status
        expect(pendingMathHomework.length, 2);
      });

      test('should filter by class and submission requirement', () {
        final submissionRequiredHomework = testHomeworkList
            .where((hw) => hw.classId != null && hw.requiresSubmission == true)
            .toList();

        // All test homework requires submission
        expect(submissionRequiredHomework.length, 4);
      });

      test('should combine class filter with assignment type filter', () {
        final classAssignmentsInMath = testHomeworkList
            .where(
              (hw) =>
                  hw.classId == 'class_math_001' &&
                  hw.assignmentType == AssignmentType.classAssignment,
            )
            .toList();

        expect(classAssignmentsInMath.length, 2);
        expect(
          classAssignmentsInMath.every((hw) => hw.subject == 'Mathematics'),
          isTrue,
        );
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle homework with null class ID', () {
        final homeworkWithNullClass = testHomeworkList
            .where((hw) => hw.classId == null)
            .toList();

        expect(
          homeworkWithNullClass.length,
          2,
        ); // Individual and group assignments
        expect(
          homeworkWithNullClass.every(
            (hw) => hw.assignmentType != AssignmentType.classAssignment,
          ),
          isTrue,
        );
      });

      test('should handle empty class list', () {
        final emptyClassList = <ClassModel>[];
        final classIds = emptyClassList.map((c) => c.id).toSet();

        final matchingHomework = testHomeworkList
            .where((hw) => hw.classId != null && classIds.contains(hw.classId))
            .toList();

        expect(matchingHomework, isEmpty);
      });

      test('should handle empty homework list', () {
        final emptyHomeworkList = <HomeworkModel>[];
        final mathClassHomework = emptyHomeworkList
            .where((hw) => hw.classId == 'class_math_001')
            .toList();

        expect(mathClassHomework, isEmpty);
      });
    });
  });
}

/// Helper function to create test class
ClassModel _createTestClass(String id, String name, String subject) {
  return ClassModel(
    id: id,
    name: name,
    subject: subject,
    type: ClassroomType.coreSubject,
    teacherId: 'teacher_001',
    teacherName: 'Test Teacher',
    studentIds: ['student_001', 'student_002', 'student_003'],
    createdAt: DateTime.now().subtract(const Duration(days: 30)),
    isActive: true,
  );
}

/// Helper function to create test homework
HomeworkModel _createTestHomework(
  String id,
  String subject,
  String title,
  AssignmentType assignmentType, {
  String? classId,
  List<String>? assignedUserIds,
}) {
  return HomeworkModel(
    id: id,
    subject: subject,
    title: title,
    description: 'Test homework description',
    assignedAt: DateTime.now().subtract(const Duration(days: 1)),
    dueAt: DateTime.now().add(const Duration(days: 7)),
    requiresSubmission: true,
    submissionType: SubmissionType.online,
    status: HomeworkStatus.pending,
    resourceUrls: [],
    teacherNote: 'Test teacher note',
    classId: classId,
    teacherId: 'teacher_001',
    assignmentType: assignmentType,
    assignedUserIds: assignedUserIds ?? [],
  );
}
