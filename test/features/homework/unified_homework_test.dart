import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:scholara_student/features/homework/services/homework_filter_service.dart';
import 'package:scholara_student/features/homework/controllers/unified_homework_controller.dart';
import 'package:scholara_student/features/homework/models/homework_filter_config.dart';
import 'package:scholara_student/features/homework/models/homework_model.dart';
import 'package:scholara_student/core/enums/homework/assignment_type.dart';

void main() {
  group('Unified Homework System Tests', () {
    late HomeworkFilterService filterService;

    setUp(() {
      filterService = HomeworkFilterService();
      filterService.initialize();
    });

    tearDown(() {
      filterService.reset();
    });

    group('HomeworkFilterService', () {
      test('should initialize with default filters', () {
        expect(filterService.getAllFilters().length, equals(4));

        final allFilter = filterService.getFilter(
          UnifiedHomeworkFilterType.all,
        );
        expect(allFilter, isNotNull);
        expect(allFilter!.isDefault, isTrue);
      });

      test('should register and unregister filters correctly', () {
        const testConfig = HomeworkFilterConfig(
          type: UnifiedHomeworkFilterType.individual,
          label: 'Test Filter',
          shortLabel: 'Test',
          description: 'Test description',
          icon: Icons.assignment,
          color: Colors.red,
          assignmentType: AssignmentType.individual,
        );

        filterService.registerFilter(testConfig);
        final retrieved = filterService.getFilter(
          UnifiedHomeworkFilterType.individual,
        );
        expect(retrieved?.label, equals('Test Filter'));

        filterService.unregisterFilter(UnifiedHomeworkFilterType.individual);
        final afterUnregister = filterService.getFilter(
          UnifiedHomeworkFilterType.individual,
        );
        expect(afterUnregister, isNull);
      });

      test('should get default filter correctly', () {
        final defaultFilter = filterService.getDefaultFilter();
        expect(defaultFilter.type, equals(UnifiedHomeworkFilterType.all));
        expect(defaultFilter.isDefault, isTrue);
      });

      test('should validate filter transitions', () {
        expect(
          filterService.canTransitionTo(
            UnifiedHomeworkFilterType.all,
            UnifiedHomeworkFilterType.classAssignments,
          ),
          isTrue,
        );

        expect(
          filterService.canTransitionTo(
            UnifiedHomeworkFilterType.individual,
            UnifiedHomeworkFilterType.group,
          ),
          isTrue,
        );
      });

      test('should calculate filter counts correctly', () {
        final mockHomework = [
          createMockHomework(AssignmentType.classAssignment),
          createMockHomework(AssignmentType.classAssignment),
          createMockHomework(AssignmentType.individual),
          createMockHomework(AssignmentType.group),
        ];

        final counts = filterService.calculateFilterCounts(mockHomework);

        expect(counts[UnifiedHomeworkFilterType.all], equals(4));
        expect(counts[UnifiedHomeworkFilterType.classAssignments], equals(2));
        expect(counts[UnifiedHomeworkFilterType.individual], equals(1));
        expect(counts[UnifiedHomeworkFilterType.group], equals(1));
      });

      test('should apply filters correctly', () {
        final mockHomework = [
          createMockHomework(AssignmentType.classAssignment, classId: 'class1'),
          createMockHomework(AssignmentType.classAssignment, classId: 'class2'),
          createMockHomework(AssignmentType.individual),
          createMockHomework(AssignmentType.group),
        ];

        // Test "all" filter
        final allFiltered = filterService.applyFilters(
          mockHomework,
          UnifiedHomeworkFilterType.all,
          null,
        );
        expect(allFiltered.length, equals(4));

        // Test class assignments filter
        final classFiltered = filterService.applyFilters(
          mockHomework,
          UnifiedHomeworkFilterType.classAssignments,
          null,
        );
        expect(classFiltered.length, equals(2));

        // Test class assignments filter with specific class
        final specificClassFiltered = filterService.applyFilters(
          mockHomework,
          UnifiedHomeworkFilterType.classAssignments,
          'class1',
        );
        expect(specificClassFiltered.length, equals(1));

        // Test individual filter
        final individualFiltered = filterService.applyFilters(
          mockHomework,
          UnifiedHomeworkFilterType.individual,
          null,
        );
        expect(individualFiltered.length, equals(1));
      });

      test('should cache filter counts', () {
        final mockHomework = [
          createMockHomework(AssignmentType.classAssignment),
          createMockHomework(AssignmentType.individual),
        ];

        // Calculate with cache key
        final counts1 = filterService.calculateFilterCounts(
          mockHomework,
          cacheKey: 'test_cache',
        );

        // Calculate again with same cache key
        final counts2 = filterService.calculateFilterCounts(
          [], // Empty list, but should return cached result
          cacheKey: 'test_cache',
        );

        expect(counts1, equals(counts2));
        expect(counts2[UnifiedHomeworkFilterType.all], equals(2));
      });

      test('should clear cache correctly', () {
        final mockHomework = [
          createMockHomework(AssignmentType.classAssignment),
        ];

        filterService.calculateFilterCounts(mockHomework, cacheKey: 'test');
        filterService.clearCache();

        // After clearing cache, should recalculate
        final newCounts = filterService.calculateFilterCounts(
          [],
          cacheKey: 'test',
        );
        expect(newCounts[UnifiedHomeworkFilterType.all], equals(0));
      });
    });

    group('UnifiedHomeworkFilterType Extensions', () {
      test('should provide correct configuration data', () {
        final allType = UnifiedHomeworkFilterType.all;
        expect(allType.displayLabel, equals('All Homework'));
        expect(allType.displayShortLabel, equals('All'));
        expect(allType.requiresClassSelection, isFalse);

        final classType = UnifiedHomeworkFilterType.classAssignments;
        expect(classType.displayLabel, equals('Class Assignments'));
        expect(classType.requiresClassSelection, isTrue);
      });

      test('should provide correct error and empty state messages', () {
        final allType = UnifiedHomeworkFilterType.all;
        expect(
          allType.emptyStateMessage,
          contains('No homework assignments found'),
        );
        expect(
          allType.getErrorMessage('test error'),
          contains('Unable to load'),
        );

        final classType = UnifiedHomeworkFilterType.classAssignments;
        expect(classType.emptyStateMessage, contains('No class assignments'));
      });
    });

    group('Filter Configuration', () {
      test('should get configuration for filter types', () {
        final config = HomeworkFilterConfig.getConfig(
          UnifiedHomeworkFilterType.all,
        );
        expect(config, isNotNull);
        expect(config!.type, equals(UnifiedHomeworkFilterType.all));
        expect(config.isDefault, isTrue);
      });

      test('should get all configurations', () {
        final configs = HomeworkFilterConfig.getAllConfigs();
        expect(configs.length, equals(4));
        expect(
          configs.map((c) => c.type).toSet(),
          equals(UnifiedHomeworkFilterType.values.toSet()),
        );
      });

      test('should identify filters requiring class selection', () {
        expect(
          HomeworkFilterConfig.requiresClassSelection(
            UnifiedHomeworkFilterType.classAssignments,
          ),
          isTrue,
        );
        expect(
          HomeworkFilterConfig.requiresClassSelection(
            UnifiedHomeworkFilterType.all,
          ),
          isFalse,
        );
      });
    });
  });
}

// Helper function to create mock homework for testing
HomeworkModel createMockHomework(AssignmentType type, {String? classId}) {
  return HomeworkModel.fromJson({
    'id': 'test_${DateTime.now().millisecondsSinceEpoch}',
    'title': 'Test Homework',
    'description': 'Test Description',
    'assignmentType': type.name,
    'classId': classId ?? 'default_class',
    'assignedAt': DateTime.now().toIso8601String(),
    'dueDate': DateTime.now().add(const Duration(days: 7)).toIso8601String(),
    'status': 'pending',
    'submissionRequired': true,
    'submissionId': null,
    'teacherId': 'test_teacher',
    'assignedUserIds': ['test_user'],
    'resources': [],
  });
}
