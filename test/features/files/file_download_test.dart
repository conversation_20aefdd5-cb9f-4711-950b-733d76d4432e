import 'package:flutter_test/flutter_test.dart';
import 'package:scholara_student/features/files/models/download_progress_model.dart';
import 'package:scholara_student/features/files/enums/download_state.dart';

void main() {
  group('DownloadProgressModel Tests', () {
    test('should create initial download progress', () {
      const id = 'test_download';

      final progress = DownloadProgressModel.initial(id);

      expect(progress.id, equals(id));
      expect(progress.state, equals(DownloadState.notDownloaded));
      expect(progress.progress, equals(0.0));
      expect(progress.downloadedBytes, equals(0));
    });

    test('should create started download progress', () {
      const id = 'test_download';

      final progress = DownloadProgressModel.started(id);

      expect(progress.id, equals(id));
      expect(progress.state, equals(DownloadState.downloading));
      expect(progress.progress, equals(0.0));
      expect(progress.downloadedBytes, equals(0));
      expect(progress.startedAt, isNotNull);
    });

    test('should create completed download progress', () {
      const id = 'test_download';
      const totalBytes = 1024;

      final progress = DownloadProgressModel.completed(id, totalBytes);

      expect(progress.id, equals(id));
      expect(progress.state, equals(DownloadState.downloaded));
      expect(progress.progress, equals(1.0));
      expect(progress.downloadedBytes, equals(totalBytes));
      expect(progress.totalBytes, equals(totalBytes));
      expect(progress.completedAt, isNotNull);
    });

    test('should create failed download progress', () {
      const id = 'test_download';
      const errorMessage = 'Network error';

      final progress = DownloadProgressModel.failed(id, errorMessage);

      expect(progress.id, equals(id));
      expect(progress.state, equals(DownloadState.failed));
      expect(progress.progress, equals(0.0));
      expect(progress.downloadedBytes, equals(0));
      expect(progress.errorMessage, equals(errorMessage));
      expect(progress.completedAt, isNotNull);
    });

    test('should format file sizes correctly', () {
      final progress1 = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
        totalBytes: 1024,
      );

      final progress2 = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 1536, // 1.5 KB
        totalBytes: 3072, // 3 KB
      );

      final progress3 = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 1572864, // 1.5 MB
        totalBytes: 3145728, // 3 MB
      );

      expect(progress1.downloadedSizeString, equals('512 B'));
      expect(progress1.totalSizeString, equals('1.0 KB'));

      expect(progress2.downloadedSizeString, equals('1.5 KB'));
      expect(progress2.totalSizeString, equals('3.0 KB'));

      expect(progress3.downloadedSizeString, equals('1.5 MB'));
      expect(progress3.totalSizeString, equals('3.0 MB'));
    });

    test('should format progress percentage correctly', () {
      final progress1 = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.0,
        downloadedBytes: 0,
      );

      final progress2 = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
      );

      final progress3 = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.756,
        downloadedBytes: 756,
      );

      expect(progress1.progressPercentageString, equals('0.0%'));
      expect(progress2.progressPercentageString, equals('50.0%'));
      expect(progress3.progressPercentageString, equals('75.6%'));
    });

    test('should format estimated time remaining correctly', () {
      final progress1 = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
        estimatedTimeRemainingSeconds: 30,
      );

      final progress2 = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
        estimatedTimeRemainingSeconds: 120, // 2 minutes
      );

      final progress3 = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
        estimatedTimeRemainingSeconds: 3600, // 1 hour
      );

      expect(progress1.estimatedTimeRemainingString, equals('30s'));
      expect(progress2.estimatedTimeRemainingString, equals('2m'));
      expect(progress3.estimatedTimeRemainingString, equals('1h'));
    });

    test('should correctly identify progress states', () {
      final downloading = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
      );

      final completed = DownloadProgressModel.completed('test', 1024);
      final failed = DownloadProgressModel.failed('test', 'Error');

      expect(downloading.isInProgress, isTrue);
      expect(downloading.isComplete, isFalse);
      expect(downloading.hasFailed, isFalse);

      expect(completed.isInProgress, isFalse);
      expect(completed.isComplete, isTrue);
      expect(completed.hasFailed, isFalse);

      expect(failed.isInProgress, isFalse);
      expect(failed.isComplete, isFalse);
      expect(failed.hasFailed, isTrue);
    });

    test('should copy with updated properties', () {
      final original = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
      );

      final updated = original.copyWith(progress: 0.75, downloadedBytes: 768);

      expect(updated.id, equals(original.id));
      expect(updated.state, equals(original.state));
      expect(updated.progress, equals(0.75));
      expect(updated.downloadedBytes, equals(768));
    });

    test('should handle unknown size gracefully', () {
      final progress = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
        totalBytes: null,
      );

      expect(progress.totalSizeString, equals('Unknown size'));
    });

    test('should handle unknown speed gracefully', () {
      final progress = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
        speedBytesPerSecond: null,
      );

      expect(progress.speedString, equals('Unknown speed'));
    });

    test('should handle unknown time remaining gracefully', () {
      final progress = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
        estimatedTimeRemainingSeconds: null,
      );

      expect(progress.estimatedTimeRemainingString, equals('Unknown time'));
    });
  });

  group('Download Progress Equality Tests', () {
    test('should be equal when IDs match', () {
      final progress1 = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
      );

      final progress2 = DownloadProgressModel(
        id: 'test',
        state: DownloadState.downloaded,
        progress: 1.0,
        downloadedBytes: 1024,
      );

      expect(progress1, equals(progress2));
      expect(progress1.hashCode, equals(progress2.hashCode));
    });

    test('should not be equal when IDs differ', () {
      final progress1 = DownloadProgressModel(
        id: 'test1',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
      );

      final progress2 = DownloadProgressModel(
        id: 'test2',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
      );

      expect(progress1, isNot(equals(progress2)));
      expect(progress1.hashCode, isNot(equals(progress2.hashCode)));
    });
  });

  group('Download Progress String Representation Tests', () {
    test('should provide meaningful string representation', () {
      final progress = DownloadProgressModel(
        id: 'test_download',
        state: DownloadState.downloading,
        progress: 0.5,
        downloadedBytes: 512,
        totalBytes: 1024,
      );

      final stringRep = progress.toString();

      expect(stringRep, contains('test_download'));
      expect(stringRep, contains('downloading'));
      expect(stringRep, contains('0.5'));
      expect(stringRep, contains('512'));
      expect(stringRep, contains('1024'));
    });
  });
}
