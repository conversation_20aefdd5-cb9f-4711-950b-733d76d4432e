import '../enums/activity_type.dart';

/// Model representing an activity in the classroom feed
/// Activities include homework, notices, announcements, quizzes, etc.
class ActivityModel {
  /// Unique identifier for the activity
  final String id;

  /// ID of the classroom this activity belongs to
  final String classroomId;

  /// Type of activity (homework, notice, announcement, etc.)
  final ActivityType type;

  /// Title of the activity
  final String title;

  /// Description or content of the activity (optional)
  final String? description;

  /// ID of the user who created this activity
  final String createdById;

  /// Name of the user who created this activity (for display)
  final String createdByName;

  /// When the activity was created
  final DateTime createdAt;

  /// When the activity was last updated
  final DateTime? updatedAt;

  /// Reference ID to the actual content (e.g., homework ID, quiz ID)
  /// Used for navigation to the specific screen
  final String? referenceId;

  /// Additional metadata for the activity
  final Map<String, dynamic>? metadata;

  /// Whether the activity is pinned (important announcements)
  final bool isPinned;

  /// Whether the activity is active/visible
  final bool isActive;

  const ActivityModel({
    required this.id,
    required this.classroomId,
    required this.type,
    required this.title,
    this.description,
    required this.createdById,
    required this.createdByName,
    required this.createdAt,
    this.updatedAt,
    this.referenceId,
    this.metadata,
    this.isPinned = false,
    this.isActive = true,
  });

  /// Create an ActivityModel from JSON
  factory ActivityModel.fromJson(Map<String, dynamic> json) {
    return ActivityModel(
      id: json['id'] as String,
      classroomId: json['classroomId'] as String,
      type: ActivityTypeExtension.fromString(json['type'] as String),
      title: json['title'] as String,
      description: json['description'] as String?,
      createdById: json['createdById'] as String,
      createdByName: json['createdByName'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      referenceId: json['referenceId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      isPinned: json['isPinned'] as bool? ?? false,
      isActive: json['isActive'] as bool? ?? true,
    );
  }

  /// Convert ActivityModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'classroomId': classroomId,
      'type': type.value,
      'title': title,
      'description': description,
      'createdById': createdById,
      'createdByName': createdByName,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'referenceId': referenceId,
      'metadata': metadata,
      'isPinned': isPinned,
      'isActive': isActive,
    };
  }

  /// Create a copy of this ActivityModel with updated fields
  ActivityModel copyWith({
    String? id,
    String? classroomId,
    ActivityType? type,
    String? title,
    String? description,
    String? createdById,
    String? createdByName,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? referenceId,
    Map<String, dynamic>? metadata,
    bool? isPinned,
    bool? isActive,
  }) {
    return ActivityModel(
      id: id ?? this.id,
      classroomId: classroomId ?? this.classroomId,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      createdById: createdById ?? this.createdById,
      createdByName: createdByName ?? this.createdByName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      referenceId: referenceId ?? this.referenceId,
      metadata: metadata ?? this.metadata,
      isPinned: isPinned ?? this.isPinned,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ActivityModel &&
        other.id == id &&
        other.classroomId == classroomId &&
        other.type == type &&
        other.title == title &&
        other.description == description &&
        other.createdById == createdById &&
        other.createdByName == createdByName &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.referenceId == referenceId &&
        _mapEquals(other.metadata, metadata) &&
        other.isPinned == isPinned &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      classroomId,
      type,
      title,
      description,
      createdById,
      createdByName,
      createdAt,
      updatedAt,
      referenceId,
      metadata != null ? Object.hashAll(metadata!.entries) : null,
      isPinned,
      isActive,
    );
  }

  bool _mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'ActivityModel(id: $id, type: ${type.label}, title: $title, classroomId: $classroomId)';
  }

  /// Helper methods for activity functionality

  /// Returns a formatted time string for display
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Returns whether this activity was created today
  bool get isToday {
    final now = DateTime.now();
    return createdAt.year == now.year &&
        createdAt.month == now.month &&
        createdAt.day == now.day;
  }

  /// Returns whether this activity was updated recently (within 24 hours)
  bool get isRecentlyUpdated {
    if (updatedAt == null) return false;
    final now = DateTime.now();
    return now.difference(updatedAt!).inHours < 24;
  }
}
