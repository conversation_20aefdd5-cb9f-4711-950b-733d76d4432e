import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/widgets/responsive/responsive_padding.dart';
import '../models/class_model.dart';
import '../models/activity_model.dart';
import '../mock/mock_classes.dart';
import '../mock/mock_activities.dart';
import '../enums/activity_type.dart';
import '../widgets/activity_tile.dart';
import '../widgets/activity_filter_chips.dart';
import '../widgets/activity_search_bar.dart';

/// Screen displaying full activity feed for a classroom with filters and search
class ActivityFeedScreen extends StatefulWidget {
  /// ID of the classroom to display activities for
  final String classroomId;

  const ActivityFeedScreen({super.key, required this.classroomId});

  @override
  State<ActivityFeedScreen> createState() => _ActivityFeedScreenState();
}

class _ActivityFeedScreenState extends State<ActivityFeedScreen> {
  /// Current classroom data
  ClassModel? _classroom;

  /// All activities for this classroom
  List<ActivityModel> _allActivities = [];

  /// Current search query
  String _searchQuery = '';

  /// Currently selected activity type filter
  ActivityType? _selectedFilter;

  /// Filtered list of activities based on search and filter
  List<ActivityModel> get _filteredActivities {
    var filtered = _allActivities;

    // Apply type filter
    if (_selectedFilter != null) {
      filtered = filtered
          .where((activity) => activity.type == _selectedFilter)
          .toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((activity) {
        final query = _searchQuery.toLowerCase();
        return activity.title.toLowerCase().contains(query) ||
            (activity.description?.toLowerCase().contains(query) ?? false) ||
            activity.createdByName.toLowerCase().contains(query);
      }).toList();
    }

    return filtered;
  }

  @override
  void initState() {
    super.initState();
    _loadActivityData();
  }

  /// Load classroom and activity data
  void _loadActivityData() {
    try {
      // Find classroom by ID
      _classroom = mockClassesList.firstWhere(
        (classroom) => classroom.id == widget.classroomId,
      );

      // Get all activities for this classroom
      _allActivities = mockActivitiesList
          .where((activity) => activity.classroomId == widget.classroomId)
          .toList();

      setState(() {});
    } catch (e) {
      debugPrint('Classroom not found: ${widget.classroomId}');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Show loading or error state if classroom not found
    if (_classroom == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Activity Feed')),
        body: const Center(child: Text('Classroom not found')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '${_classroom!.name} Activity',
          style: theme.textTheme.titleLarge,
        ),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
      ),
      body: ResponsivePadding(
        mobile: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search bar
            ActivitySearchBar(
              onSearchChanged: (query) {
                setState(() {
                  _searchQuery = query;
                });
              },
            ),

            SizedBox(height: 16.h),

            // Filter chips
            ActivityFilterChips(
              selectedFilter: _selectedFilter,
              onFilterChanged: (filter) {
                setState(() {
                  _selectedFilter = filter;
                });
              },
            ),

            SizedBox(height: 16.h),

            // Results count
            Text(
              '${_filteredActivities.length} activit${_filteredActivities.length != 1 ? 'ies' : 'y'}',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),

            SizedBox(height: 12.h),

            // Activities list
            Expanded(
              child: _filteredActivities.isEmpty
                  ? _buildEmptyState(theme)
                  : ListView.separated(
                      itemCount: _filteredActivities.length,
                      separatorBuilder: (context, index) =>
                          SizedBox(height: 12.h),
                      itemBuilder: (context, index) {
                        final activity = _filteredActivities[index];
                        return ActivityTile(
                          activity: activity,
                          onTap: () => _handleActivityTap(activity),
                          isCompact: false, // Full version for activity feed
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _handleCreateAnnouncement,
        icon: const Icon(Symbols.add),
        label: const Text('Announcement'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
    );
  }

  /// Build empty state widget when no activities match the filter/search
  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Symbols.timeline,
            size: 64.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          SizedBox(height: 16.h),
          Text(
            _searchQuery.isNotEmpty || _selectedFilter != null
                ? 'No activities found'
                : 'No activities yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            _searchQuery.isNotEmpty || _selectedFilter != null
                ? 'Try adjusting your search or filters'
                : 'Activities will appear here as they are created',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Handle activity item tap
  void _handleActivityTap(ActivityModel activity) {
    if (!activity.type.isClickable) return;

    // TODO: Implement navigation to specific activity details
    // This will be implemented when individual activity detail screens are created
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${activity.type.label} details coming soon')),
    );
  }

  /// Handle create announcement action
  void _handleCreateAnnouncement() {
    // TODO: Check if user has permission to create announcements
    // For now, show a dialog or navigate to create announcement screen
    _showCreateAnnouncementDialog();
  }

  /// Show create announcement dialog
  void _showCreateAnnouncementDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Announcement'),
        content: const Text('Announcement creation feature coming soon'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
