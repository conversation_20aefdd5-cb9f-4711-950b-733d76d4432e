import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/widgets/responsive/responsive_padding.dart';
import '../models/class_model.dart';
import '../mock/mock_classes.dart';

/// Placeholder screen for classroom discussions
/// This will be fully implemented when the Discussion feature is developed
class DiscussionScreen extends StatefulWidget {
  /// ID of the classroom to display discussions for
  final String classroomId;

  const DiscussionScreen({super.key, required this.classroomId});

  @override
  State<DiscussionScreen> createState() => _DiscussionScreenState();
}

class _DiscussionScreenState extends State<DiscussionScreen> {
  /// Current classroom data
  ClassModel? _classroom;

  @override
  void initState() {
    super.initState();
    _loadClassroomData();
  }

  /// Load classroom data
  void _loadClassroomData() {
    try {
      _classroom = mockClassesList.firstWhere(
        (classroom) => classroom.id == widget.classroomId,
      );
      setState(() {});
    } catch (e) {
      debugPrint('Classroom not found: ${widget.classroomId}');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _classroom != null
              ? '${_classroom!.name} Discussions'
              : 'Discussions',
          style: theme.textTheme.titleLarge,
        ),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Symbols.search),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Search coming soon')),
              );
            },
          ),
        ],
      ),
      body: ResponsivePadding(
        mobile: EdgeInsets.all(16.w),
        child: _buildPlaceholderContent(theme),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _handleCreatePost,
        icon: const Icon(Symbols.add),
        label: const Text('New Post'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
    );
  }

  /// Build placeholder content for discussion screen
  Widget _buildPlaceholderContent(ThemeData theme) {
    return Column(
      children: [
        // Post type tabs placeholder
        _buildPostTypeTabs(theme),

        SizedBox(height: 24.h),

        // Main content area
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Icon
                Container(
                  padding: EdgeInsets.all(24.w),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(24.r),
                  ),
                  child: Icon(
                    Symbols.forum,
                    size: 64.sp,
                    color: theme.colorScheme.primary,
                  ),
                ),

                SizedBox(height: 24.h),

                // Title
                Text(
                  'Class Discussions',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),

                SizedBox(height: 12.h),

                // Description
                Text(
                  'Ask questions, share resources, and\ncollaborate with your classmates',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),

                SizedBox(height: 32.h),

                // Coming soon badge
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 8.h,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.secondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: theme.colorScheme.secondary.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    'Coming Soon',
                    style: theme.textTheme.labelLarge?.copyWith(
                      color: theme.colorScheme.secondary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                SizedBox(height: 24.h),

                // Feature preview
                _buildFeaturePreview(theme),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build post type tabs placeholder
  Widget _buildPostTypeTabs(ThemeData theme) {
    final tabs = ['All', 'Announcements', 'Doubts', 'Resources', 'General'];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: tabs.map((tab) {
          final isSelected = tab == 'All';
          return Padding(
            padding: EdgeInsets.only(right: 8.w),
            child: FilterChip(
              label: Text(
                tab,
                style: theme.textTheme.labelMedium?.copyWith(
                  color: isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
              selected: isSelected,
              onSelected: (_) {},
              backgroundColor: theme.colorScheme.surface,
              selectedColor: theme.colorScheme.primary.withValues(alpha: 0.1),
              side: BorderSide(
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Build feature preview section
  Widget _buildFeaturePreview(ThemeData theme) {
    final features = [
      'Ask Questions and Get Answers',
      'Share Study Resources',
      'Instructor Announcements',
      'Like and Reply to Posts',
      'File Attachments Support',
    ];

    return Column(
      children: features.map((feature) {
        return Padding(
          padding: EdgeInsets.only(bottom: 8.h),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Symbols.check_circle,
                size: 16.sp,
                color: theme.colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              Text(
                feature,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// Handle create post action
  void _handleCreatePost() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Create post feature coming soon')),
    );
  }
}
