import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different types of classrooms in the Scholara system
enum ClassroomType {
  /// Core academic subjects (Math, Science, English, etc.)
  coreSubject,
  
  /// Elective subjects (Art, Music, etc.)
  elective,
  
  /// Student clubs (Chess, Debate, etc.)
  club,
  
  /// Sports or activity teams
  team,
  
  /// Project groups for collaborative work
  projectGroup,
  
  /// Grade-level classrooms (10th, 11th, 12th)
  grade,
  
  /// Custom classroom type for future extensibility
  custom,
}

/// Extension to provide additional functionality for ClassroomType
extension ClassroomTypeExtension on ClassroomType {
  /// Returns a human-readable label for the classroom type
  String get label {
    switch (this) {
      case ClassroomType.coreSubject:
        return 'Core Subject';
      case ClassroomType.elective:
        return 'Elective';
      case ClassroomType.club:
        return 'Club';
      case ClassroomType.team:
        return 'Team';
      case ClassroomType.projectGroup:
        return 'Project Group';
      case ClassroomType.grade:
        return 'Grade';
      case ClassroomType.custom:
        return 'Custom';
    }
  }

  /// Returns a short label for the classroom type (for UI space constraints)
  String get shortLabel {
    switch (this) {
      case ClassroomType.coreSubject:
        return 'Core';
      case ClassroomType.elective:
        return 'Elective';
      case ClassroomType.club:
        return 'Club';
      case ClassroomType.team:
        return 'Team';
      case ClassroomType.projectGroup:
        return 'Project';
      case ClassroomType.grade:
        return 'Grade';
      case ClassroomType.custom:
        return 'Custom';
    }
  }

  /// Returns the default color associated with the classroom type
  Color get defaultColor {
    switch (this) {
      case ClassroomType.coreSubject:
        return const Color(0xFF2196F3); // Blue
      case ClassroomType.elective:
        return const Color(0xFF9C27B0); // Purple
      case ClassroomType.club:
        return const Color(0xFF4CAF50); // Green
      case ClassroomType.team:
        return const Color(0xFFFF5722); // Deep Orange
      case ClassroomType.projectGroup:
        return const Color(0xFFFF9800); // Orange
      case ClassroomType.grade:
        return const Color(0xFF607D8B); // Blue Grey
      case ClassroomType.custom:
        return const Color(0xFF9E9E9E); // Grey
    }
  }

  /// Returns an icon associated with the classroom type
  IconData get icon {
    switch (this) {
      case ClassroomType.coreSubject:
        return Symbols.school;
      case ClassroomType.elective:
        return Symbols.palette;
      case ClassroomType.club:
        return Symbols.groups;
      case ClassroomType.team:
        return Symbols.sports;
      case ClassroomType.projectGroup:
        return Symbols.work_history;
      case ClassroomType.grade:
        return Symbols.grade;
      case ClassroomType.custom:
        return Symbols.settings;
    }
  }

  /// Returns whether this classroom type typically requires a teacher
  bool get requiresTeacher {
    switch (this) {
      case ClassroomType.coreSubject:
      case ClassroomType.elective:
      case ClassroomType.grade:
        return true;
      case ClassroomType.club:
      case ClassroomType.team:
      case ClassroomType.projectGroup:
      case ClassroomType.custom:
        return false;
    }
  }

  /// Returns a description of the classroom type
  String get description {
    switch (this) {
      case ClassroomType.coreSubject:
        return 'Essential academic subjects like Mathematics, Science, English';
      case ClassroomType.elective:
        return 'Optional subjects like Art, Music, Computer Science';
      case ClassroomType.club:
        return 'Student clubs for hobbies and interests';
      case ClassroomType.team:
        return 'Sports teams and competitive groups';
      case ClassroomType.projectGroup:
        return 'Collaborative project groups';
      case ClassroomType.grade:
        return 'Grade-level classrooms for general activities';
      case ClassroomType.custom:
        return 'Custom classroom configuration';
    }
  }

  /// Returns the string value for the classroom type (for JSON serialization)
  String get value {
    switch (this) {
      case ClassroomType.coreSubject:
        return 'core_subject';
      case ClassroomType.elective:
        return 'elective';
      case ClassroomType.club:
        return 'club';
      case ClassroomType.team:
        return 'team';
      case ClassroomType.projectGroup:
        return 'project_group';
      case ClassroomType.grade:
        return 'grade';
      case ClassroomType.custom:
        return 'custom';
    }
  }

  /// Create ClassroomType from string value
  static ClassroomType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'core_subject':
        return ClassroomType.coreSubject;
      case 'elective':
        return ClassroomType.elective;
      case 'club':
        return ClassroomType.club;
      case 'team':
        return ClassroomType.team;
      case 'project_group':
        return ClassroomType.projectGroup;
      case 'grade':
        return ClassroomType.grade;
      case 'custom':
        return ClassroomType.custom;
      default:
        return ClassroomType.coreSubject; // Default to core subject
    }
  }
}
