import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different types of activities in the classroom feed
enum ActivityType {
  /// Homework assignments
  homework,
  
  /// Notices from teachers/admins
  notice,
  
  /// Announcements (standalone messages)
  announcement,
  
  /// Quiz assignments
  quiz,
  
  /// Attendance updates
  attendance,
  
  /// Digital library resources (notes, documents)
  digitalLibrary,
  
  /// General notifications
  notification,
  
  /// Reminders for upcoming events
  reminder,
  
  /// Discussion posts
  discussion,
}

/// Extension to provide additional functionality for ActivityType
extension ActivityTypeExtension on ActivityType {
  /// Returns a human-readable label for the activity type
  String get label {
    switch (this) {
      case ActivityType.homework:
        return 'Homework';
      case ActivityType.notice:
        return 'Notice';
      case ActivityType.announcement:
        return 'Announcement';
      case ActivityType.quiz:
        return 'Quiz';
      case ActivityType.attendance:
        return 'Attendance';
      case ActivityType.digitalLibrary:
        return 'Digital Library';
      case ActivityType.notification:
        return 'Notification';
      case ActivityType.reminder:
        return 'Reminder';
      case ActivityType.discussion:
        return 'Discussion';
    }
  }

  /// Returns a short label for the activity type (for UI space constraints)
  String get shortLabel {
    switch (this) {
      case ActivityType.homework:
        return 'HW';
      case ActivityType.notice:
        return 'Notice';
      case ActivityType.announcement:
        return 'Announce';
      case ActivityType.quiz:
        return 'Quiz';
      case ActivityType.attendance:
        return 'Attend';
      case ActivityType.digitalLibrary:
        return 'Library';
      case ActivityType.notification:
        return 'Notify';
      case ActivityType.reminder:
        return 'Remind';
      case ActivityType.discussion:
        return 'Discuss';
    }
  }

  /// Returns the color associated with the activity type
  Color get color {
    switch (this) {
      case ActivityType.homework:
        return const Color(0xFF2196F3); // Blue
      case ActivityType.notice:
        return const Color(0xFFFF9800); // Orange
      case ActivityType.announcement:
        return const Color(0xFF9C27B0); // Purple
      case ActivityType.quiz:
        return const Color(0xFF4CAF50); // Green
      case ActivityType.attendance:
        return const Color(0xFF607D8B); // Blue Grey
      case ActivityType.digitalLibrary:
        return const Color(0xFF795548); // Brown
      case ActivityType.notification:
        return const Color(0xFF00BCD4); // Cyan
      case ActivityType.reminder:
        return const Color(0xFFFFC107); // Amber
      case ActivityType.discussion:
        return const Color(0xFF8BC34A); // Light Green
    }
  }

  /// Returns an icon associated with the activity type
  IconData get icon {
    switch (this) {
      case ActivityType.homework:
        return Symbols.assignment;
      case ActivityType.notice:
        return Symbols.campaign;
      case ActivityType.announcement:
        return Symbols.notifications_active;
      case ActivityType.quiz:
        return Symbols.quiz;
      case ActivityType.attendance:
        return Symbols.how_to_reg;
      case ActivityType.digitalLibrary:
        return Symbols.library_books;
      case ActivityType.notification:
        return Symbols.notifications;
      case ActivityType.reminder:
        return Symbols.schedule;
      case ActivityType.discussion:
        return Symbols.forum;
    }
  }

  /// Returns whether this activity type can be created by students
  bool get canBeCreatedByStudents {
    switch (this) {
      case ActivityType.discussion:
        return true; // Students can create discussion posts
      case ActivityType.homework:
      case ActivityType.notice:
      case ActivityType.announcement:
      case ActivityType.quiz:
      case ActivityType.attendance:
      case ActivityType.digitalLibrary:
      case ActivityType.notification:
      case ActivityType.reminder:
        return false; // Only teachers/admins can create these
    }
  }

  /// Returns whether this activity type is clickable (navigates to detail screen)
  bool get isClickable {
    switch (this) {
      case ActivityType.homework:
      case ActivityType.quiz:
      case ActivityType.digitalLibrary:
      case ActivityType.discussion:
        return true; // These have dedicated detail screens
      case ActivityType.notice:
      case ActivityType.announcement:
      case ActivityType.attendance:
      case ActivityType.notification:
      case ActivityType.reminder:
        return false; // These are informational only
    }
  }

  /// Returns a description of the activity type
  String get description {
    switch (this) {
      case ActivityType.homework:
        return 'Homework assignments and submissions';
      case ActivityType.notice:
        return 'Important notices from teachers';
      case ActivityType.announcement:
        return 'General announcements for the class';
      case ActivityType.quiz:
        return 'Quiz assignments and assessments';
      case ActivityType.attendance:
        return 'Attendance tracking and updates';
      case ActivityType.digitalLibrary:
        return 'Shared resources and study materials';
      case ActivityType.notification:
        return 'System notifications and alerts';
      case ActivityType.reminder:
        return 'Reminders for upcoming events';
      case ActivityType.discussion:
        return 'Class discussions and Q&A';
    }
  }

  /// Returns the string value for the activity type (for JSON serialization)
  String get value {
    switch (this) {
      case ActivityType.homework:
        return 'homework';
      case ActivityType.notice:
        return 'notice';
      case ActivityType.announcement:
        return 'announcement';
      case ActivityType.quiz:
        return 'quiz';
      case ActivityType.attendance:
        return 'attendance';
      case ActivityType.digitalLibrary:
        return 'digital_library';
      case ActivityType.notification:
        return 'notification';
      case ActivityType.reminder:
        return 'reminder';
      case ActivityType.discussion:
        return 'discussion';
    }
  }

  /// Create ActivityType from string value
  static ActivityType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'homework':
        return ActivityType.homework;
      case 'notice':
        return ActivityType.notice;
      case 'announcement':
        return ActivityType.announcement;
      case 'quiz':
        return ActivityType.quiz;
      case 'attendance':
        return ActivityType.attendance;
      case 'digital_library':
        return ActivityType.digitalLibrary;
      case 'notification':
        return ActivityType.notification;
      case 'reminder':
        return ActivityType.reminder;
      case 'discussion':
        return ActivityType.discussion;
      default:
        return ActivityType.notification; // Default to notification
    }
  }
}
