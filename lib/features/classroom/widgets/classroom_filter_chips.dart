import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../enums/classroom_type.dart';

/// Widget displaying filter chips for classroom types
class ClassroomFilterChips extends StatelessWidget {
  /// Currently selected filter type
  final ClassroomType? selectedFilter;

  /// Callback when filter selection changes
  final Function(ClassroomType? filter) onFilterChanged;

  const ClassroomFilterChips({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          // "All" filter chip
          _buildFilterChip(
            context: context,
            theme: theme,
            label: 'All',
            isSelected: selectedFilter == null,
            onTap: () => onFilterChanged(null),
          ),

          SizedBox(width: 8.w),

          // Individual classroom type filter chips
          ...ClassroomType.values.map((type) {
            return Padding(
              padding: EdgeInsets.only(right: 8.w),
              child: _buildFilterChip(
                context: context,
                theme: theme,
                label: type.label,
                isSelected: selectedFilter == type,
                onTap: () => onFilterChanged(type),
                color: type.defaultColor,
              ),
            );
          }),
        ],
      ),
    );
  }

  /// Build individual filter chip
  Widget _buildFilterChip({
    required BuildContext context,
    required ThemeData theme,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    Color? color,
  }) {
    final effectiveColor = color ?? theme.colorScheme.primary;

    return FilterChip(
      label: Text(
        label,
        style: theme.textTheme.labelMedium?.copyWith(
          color: isSelected
              ? effectiveColor.computeLuminance() > 0.5
                    ? Colors.black
                    : Colors.white
              : theme.colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      onSelected: (_) => onTap(),
      backgroundColor: theme.colorScheme.surface,
      selectedColor: effectiveColor.withValues(alpha: 0.2),
      checkmarkColor: effectiveColor,
      side: BorderSide(
        color: isSelected
            ? effectiveColor
            : theme.colorScheme.outline.withValues(alpha: 0.3),
        width: isSelected ? 1.5.w : 1.w,
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
    );
  }
}
