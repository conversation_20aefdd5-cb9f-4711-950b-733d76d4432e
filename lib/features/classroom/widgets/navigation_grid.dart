import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// 2x3 navigation grid for classroom features
class NavigationGrid extends StatelessWidget {
  /// ID of the current classroom
  final String classroomId;

  /// Callback when a navigation item is tapped
  final Function(String section) onNavigate;

  const NavigationGrid({
    super.key,
    required this.classroomId,
    required this.onNavigate,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Define navigation items (2x3 grid)
    final navigationItems = [
      _NavigationItem(
        id: 'homework',
        title: 'Homework',
        icon: Symbols.assignment,
      ),

      _NavigationItem(id: 'quizzes', title: 'Quizzes', icon: Symbols.quiz),
      _NavigationItem(id: 'notices', title: 'Notices', icon: Symbols.campaign),
      _NavigationItem(
        id: 'discussions',
        title: 'Discussions',
        icon: Symbols.forum,
      ),
      _NavigationItem(
        id: 'attendance',
        title: 'Attendance',
        icon: Symbols.how_to_reg,
      ),
      _NavigationItem(
        id: 'digital_library',
        title: 'Library',
        icon: Symbols.library_books,
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 4.w,
        mainAxisSpacing: 4.h,
        childAspectRatio: 1,
      ),
      itemCount: navigationItems.length,
      itemBuilder: (context, index) {
        final item = navigationItems[index];
        return _buildNavigationCard(context, theme, item);
      },
    );
  }

  /// Build individual navigation card
  Widget _buildNavigationCard(
    BuildContext context,
    ThemeData theme,
    _NavigationItem item,
  ) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: () => onNavigate(item.id),
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(16.r)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(item.icon, size: 28.sp),
              ),

              SizedBox(height: 12.h),

              // Title
              Text(
                item.title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Data class for navigation items
class _NavigationItem {
  final String id;
  final String title;
  final IconData icon;

  const _NavigationItem({
    required this.id,
    required this.title,
    required this.icon,
  });
}
