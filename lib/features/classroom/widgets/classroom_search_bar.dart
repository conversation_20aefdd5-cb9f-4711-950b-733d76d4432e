import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Search bar widget for filtering classrooms
class ClassroomSearchBar extends StatefulWidget {
  /// Callback when search query changes
  final Function(String query) onSearchChanged;

  const ClassroomSearchBar({super.key, required this.onSearchChanged});

  @override
  State<ClassroomSearchBar> createState() => _ClassroomSearchBarState();
}

class _ClassroomSearchBarState extends State<ClassroomSearchBar> {
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: TextField(
        controller: _controller,
        onChanged: widget.onSearchChanged,
        decoration: InputDecoration(
          hintText: 'Search classrooms...',
          hintStyle: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          prefixIcon: Icon(
            Symbols.search,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            size: 20.sp,
          ),
          suffixIcon: _controller.text.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Symbols.clear,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                    size: 20.sp,
                  ),
                  onPressed: () {
                    _controller.clear();
                    widget.onSearchChanged('');
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 12.h,
          ),
        ),
        style: theme.textTheme.bodyMedium,
      ),
    );
  }
}
