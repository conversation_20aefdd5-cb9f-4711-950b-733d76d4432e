import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../models/activity_model.dart';
import '../enums/activity_type.dart';

/// Tile widget for displaying individual activities
class ActivityTile extends StatelessWidget {
  /// The activity data to display
  final ActivityModel activity;

  /// Callback when the tile is tapped
  final VoidCallback onTap;

  /// Whether to show compact version (for recent feed)
  final bool isCompact;

  const ActivityTile({
    super.key,
    required this.activity,
    required this.onTap,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
        side: BorderSide(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: InkWell(
        onTap: activity.type.isClickable ? onTap : null,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(isCompact ? 12.w : 16.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Activity type icon
              Container(
                padding: EdgeInsets.all(isCompact ? 8.w : 10.w),
                decoration: BoxDecoration(
                  color: activity.type.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(isCompact ? 8.r : 10.r),
                ),
                child: Icon(
                  activity.type.icon,
                  size: isCompact ? 16.sp : 20.sp,
                  color: activity.type.color,
                ),
              ),

              SizedBox(width: 12.w),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title row with pinned indicator
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            activity.title,
                            style:
                                (isCompact
                                        ? theme.textTheme.bodyMedium
                                        : theme.textTheme.titleSmall)
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: theme.colorScheme.onSurface,
                                    ),
                            maxLines: isCompact ? 1 : 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        // Pinned indicator
                        if (activity.isPinned) ...[
                          SizedBox(width: 8.w),
                          Icon(
                            Symbols.push_pin,
                            size: 14.sp,
                            color: theme.colorScheme.primary,
                          ),
                        ],
                      ],
                    ),

                    // Description (if available and not compact)
                    if (!isCompact && activity.description != null) ...[
                      SizedBox(height: 4.h),
                      Text(
                        activity.description!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],

                    SizedBox(height: isCompact ? 4.h : 8.h),

                    // Bottom row with creator and timestamp
                    Row(
                      children: [
                        // Creator name
                        Text(
                          activity.createdByName,
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(
                              alpha: 0.6,
                            ),
                            fontWeight: FontWeight.w500,
                          ),
                        ),

                        // Separator
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 6.w),
                          width: 2.w,
                          height: 2.w,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.onSurface.withValues(
                              alpha: 0.4,
                            ),
                            shape: BoxShape.circle,
                          ),
                        ),

                        // Timestamp
                        Text(
                          activity.formattedTime,
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(
                              alpha: 0.6,
                            ),
                          ),
                        ),

                        // Recently updated indicator
                        if (activity.isRecentlyUpdated) ...[
                          SizedBox(width: 6.w),
                          Container(
                            width: 6.w,
                            height: 6.w,
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),

              // Clickable indicator
              if (activity.type.isClickable) ...[
                SizedBox(width: 8.w),
                Icon(
                  Symbols.chevron_right,
                  size: 16.sp,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
