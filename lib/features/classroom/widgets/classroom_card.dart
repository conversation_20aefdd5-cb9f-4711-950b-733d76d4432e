import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/providers/auth_providers.dart';
import '../models/class_model.dart';
import '../enums/classroom_type.dart';

/// Card widget displaying classroom information with custom styling
class ClassroomCard extends ConsumerWidget {
  /// The classroom data to display
  final ClassModel classroom;

  /// Callback when the card is tapped
  final VoidCallback onTap;

  /// Callback when a menu action is selected
  final Function(String action) onMenuAction;

  const ClassroomCard({
    super.key,
    required this.classroom,
    required this.onTap,
    required this.onMenuAction,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Get current user's primary class ID
    final currentUser = ref.watch(currentUserProvider);
    final isPrimaryClass = currentUser?.primaryClassId == classroom.id;

    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border(
              left: BorderSide(color: _getClassroomColor(), width: 4.w),
            ),
          ),
          child: Row(
            children: [
              // Avatar/Icon section
              _buildAvatar(theme),

              SizedBox(width: 12.w),

              // Content section
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Classroom name
                    Text(
                      classroom.name,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    SizedBox(height: 4.h),

                    // Instructor/Admin name
                    if (classroom.instructorDisplayName != null)
                      Text(
                        classroom.instructorDisplayName!,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                    // Description (if available, brief version)
                    if (classroom.description != null) ...[
                      SizedBox(height: 4.h),
                      Text(
                        classroom.description!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.6,
                          ),
                          fontStyle: FontStyle.italic,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],

                    SizedBox(height: 8.h),

                    // Classroom type and student count
                    Row(
                      children: [
                        // Type chip
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 2.h,
                          ),
                          decoration: BoxDecoration(
                            color: classroom.type.defaultColor.withValues(
                              alpha: 0.1,
                            ),
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Text(
                            classroom.type.shortLabel,
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: classroom.type.defaultColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),

                        SizedBox(width: 8.w),

                        // Student count
                        Icon(
                          Symbols.people,
                          size: 14.sp,
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.5,
                          ),
                        ),
                        SizedBox(width: 2.w),
                        Text(
                          '${classroom.studentIds.length}',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(
                              alpha: 0.7,
                            ),
                          ),
                        ),

                        // Primary class indicator (shows if this is user's primary class)
                        if (isPrimaryClass) ...[
                          SizedBox(width: 8.w),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 6.w,
                              vertical: 2.h,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primaryLight.withValues(
                                alpha: 0.1,
                              ),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Text(
                              'PRIMARY',
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: AppColors.primaryLight,
                                fontWeight: FontWeight.bold,
                                fontSize: 9.sp,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),

                    // TODO: Status chips (Assignment due today, Quiz tomorrow, etc.)
                    // This will be implemented when alert system is created
                  ],
                ),
              ),

              // Menu button
              PopupMenuButton<String>(
                onSelected: onMenuAction,
                icon: Icon(
                  Symbols.more_vert,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: ListTile(
                      leading: Icon(Symbols.edit),
                      title: Text('Edit'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'archive',
                    child: ListTile(
                      leading: Icon(Symbols.archive),
                      title: Text('Archive'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'leave',
                    child: ListTile(
                      leading: Icon(Symbols.exit_to_app),
                      title: Text('Leave'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  // TODO: Show delete option only for admins/creators
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(
                        Symbols.delete,
                        color: AppColors.errorLight,
                      ),
                      title: Text(
                        'Delete',
                        style: TextStyle(color: AppColors.errorLight),
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build avatar/icon section
  Widget _buildAvatar(ThemeData theme) {
    // If profile picture is available, use it
    if (classroom.profilePictureUrl != null) {
      return CircleAvatar(
        radius: 24.r,
        backgroundImage: NetworkImage(classroom.profilePictureUrl!),
        backgroundColor: classroom.type.defaultColor.withValues(alpha: 0.1),
      );
    }

    // Otherwise, use icon or default classroom type icon
    return CircleAvatar(
      radius: 24.r,
      backgroundColor: classroom.type.defaultColor.withValues(alpha: 0.1),
      child: Icon(
        classroom.type.icon,
        size: 24.sp,
        color: classroom.type.defaultColor,
      ),
    );
  }

  /// Get the effective color for the classroom based on type
  Color _getClassroomColor() {
    return classroom.type.defaultColor;
  }
}
