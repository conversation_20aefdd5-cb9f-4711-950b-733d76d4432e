import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../core/providers/auth_providers.dart';
import '../models/activity_model.dart';
import '../enums/activity_type.dart';
import '../repositories/activity_repository.dart';

/// Logger for activity controller
final _logger = Logger();

/// Provider for the activity repository instance
final activityRepositoryProvider = Provider<ActivityRepository>((ref) {
  return ActivityRepository();
});

/// Provider for current user ID from authentication
final currentUserIdProvider = Provider<String>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.user?.id ?? 'anonymous',
    loading: () => 'anonymous',
    error: (_, __) => 'anonymous',
  );
});

/// Provider to fetch all activities for a specific classroom
final classroomActivitiesProvider =
    FutureProvider.family<List<ActivityModel>, String>((
      ref,
      classroomId,
    ) async {
      final repository = ref.read(activityRepositoryProvider);

      try {
        _logger.i('Fetching activities for classroom: $classroomId');
        final activities = await repository.getActivitiesForClassroom(
          classroomId,
        );
        _logger.i(
          'Successfully fetched ${activities.length} activities for classroom $classroomId',
        );
        return activities;
      } catch (e) {
        _logger.e('Error fetching activities for classroom: $e');
        rethrow;
      }
    });

/// Provider to fetch recent activities for a classroom (for detail screen)
final recentClassroomActivitiesProvider =
    FutureProvider.family<List<ActivityModel>, String>((
      ref,
      classroomId,
    ) async {
      final repository = ref.read(activityRepositoryProvider);

      try {
        _logger.i('Fetching recent activities for classroom: $classroomId');
        final activities = await repository.getRecentActivitiesForClassroom(
          classroomId,
          limit: 10,
          dayLimit: 3,
        );
        _logger.i(
          'Successfully fetched ${activities.length} recent activities for classroom $classroomId',
        );
        return activities;
      } catch (e) {
        _logger.e('Error fetching recent activities for classroom: $e');
        rethrow;
      }
    });

/// Provider to fetch activities by type for a classroom
final activitiesByTypeProvider =
    FutureProvider.family<
      List<ActivityModel>,
      ({String classroomId, ActivityType type})
    >((ref, params) async {
      final repository = ref.read(activityRepositoryProvider);

      try {
        _logger.i(
          'Fetching ${params.type.label} activities for classroom: ${params.classroomId}',
        );
        final activities = await repository.getActivitiesByType(
          params.classroomId,
          params.type,
        );
        _logger.i(
          'Successfully fetched ${activities.length} ${params.type.label} activities',
        );
        return activities;
      } catch (e) {
        _logger.e('Error fetching activities by type: $e');
        rethrow;
      }
    });

/// Provider to fetch a specific activity by ID
final activityDetailProvider = FutureProvider.family<ActivityModel?, String>((
  ref,
  activityId,
) async {
  final repository = ref.read(activityRepositoryProvider);

  try {
    _logger.i('Fetching activity detail for ID: $activityId');
    final activity = await repository.getActivityById(activityId);
    if (activity != null) {
      _logger.i('Successfully fetched activity: ${activity.title}');
    } else {
      _logger.w('Activity not found: $activityId');
    }
    return activity;
  } catch (e) {
    _logger.e('Error fetching activity detail: $e');
    rethrow;
  }
});

/// Provider to fetch pinned activities for a classroom
final pinnedActivitiesProvider =
    FutureProvider.family<List<ActivityModel>, String>((
      ref,
      classroomId,
    ) async {
      final repository = ref.read(activityRepositoryProvider);

      try {
        _logger.i('Fetching pinned activities for classroom: $classroomId');
        final activities = await repository.getPinnedActivities(classroomId);
        _logger.i(
          'Successfully fetched ${activities.length} pinned activities for classroom $classroomId',
        );
        return activities;
      } catch (e) {
        _logger.e('Error fetching pinned activities: $e');
        rethrow;
      }
    });

/// State provider for activity filter type
final activityFilterProvider = StateProvider<ActivityType?>((ref) => null);

/// State provider for activity search query
final activitySearchProvider = StateProvider<String>((ref) => '');

/// Provider for filtered activities based on current filter and search
final filteredActivitiesProvider =
    FutureProvider.family<List<ActivityModel>, String>((
      ref,
      classroomId,
    ) async {
      final filterType = ref.watch(activityFilterProvider);
      final searchQuery = ref.watch(activitySearchProvider);

      List<ActivityModel> activities;

      // Get activities based on filter
      if (filterType != null) {
        activities = await ref.read(
          activitiesByTypeProvider((
            classroomId: classroomId,
            type: filterType,
          )).future,
        );
      } else {
        activities = await ref.read(
          classroomActivitiesProvider(classroomId).future,
        );
      }

      // Apply search filter if query is not empty
      if (searchQuery.isNotEmpty) {
        final repository = ref.read(activityRepositoryProvider);
        activities = await repository.searchActivities(
          classroomId,
          searchQuery,
        );
      }

      _logger.i(
        'Filtered activities: ${activities.length} results for classroom $classroomId',
      );
      return activities;
    });

/// Provider to create a new activity (announcement)
final createActivityProvider = FutureProvider.family<void, ActivityModel>((
  ref,
  activity,
) async {
  final repository = ref.read(activityRepositoryProvider);

  try {
    _logger.i('Creating new activity: ${activity.title}');
    await repository.createActivity(activity);
    _logger.i('Successfully created activity: ${activity.title}');

    // Invalidate relevant providers to refresh data
    ref.invalidate(classroomActivitiesProvider(activity.classroomId));
    ref.invalidate(recentClassroomActivitiesProvider(activity.classroomId));
    if (activity.isPinned) {
      ref.invalidate(pinnedActivitiesProvider(activity.classroomId));
    }
  } catch (e) {
    _logger.e('Error creating activity: $e');
    rethrow;
  }
});

/// Provider to update an activity
final updateActivityProvider = FutureProvider.family<void, ActivityModel>((
  ref,
  activity,
) async {
  final repository = ref.read(activityRepositoryProvider);

  try {
    _logger.i('Updating activity: ${activity.title}');
    await repository.updateActivity(activity);
    _logger.i('Successfully updated activity: ${activity.title}');

    // Invalidate relevant providers to refresh data
    ref.invalidate(classroomActivitiesProvider(activity.classroomId));
    ref.invalidate(recentClassroomActivitiesProvider(activity.classroomId));
    ref.invalidate(activityDetailProvider(activity.id));
    ref.invalidate(pinnedActivitiesProvider(activity.classroomId));
  } catch (e) {
    _logger.e('Error updating activity: $e');
    rethrow;
  }
});

/// Provider to delete an activity
final deleteActivityProvider =
    FutureProvider.family<void, ({String activityId, String classroomId})>((
      ref,
      params,
    ) async {
      final repository = ref.read(activityRepositoryProvider);

      try {
        _logger.i('Deleting activity: ${params.activityId}');
        await repository.deleteActivity(params.activityId);
        _logger.i('Successfully deleted activity: ${params.activityId}');

        // Invalidate relevant providers to refresh data
        ref.invalidate(classroomActivitiesProvider(params.classroomId));
        ref.invalidate(recentClassroomActivitiesProvider(params.classroomId));
        ref.invalidate(activityDetailProvider(params.activityId));
        ref.invalidate(pinnedActivitiesProvider(params.classroomId));
      } catch (e) {
        _logger.e('Error deleting activity: $e');
        rethrow;
      }
    });

/// Provider to toggle activity pin status
final toggleActivityPinProvider =
    FutureProvider.family<
      void,
      ({String activityId, String classroomId, bool isPinned})
    >((ref, params) async {
      final repository = ref.read(activityRepositoryProvider);

      try {
        _logger.i(
          '${params.isPinned ? 'Pinning' : 'Unpinning'} activity: ${params.activityId}',
        );
        await repository.toggleActivityPin(params.activityId, params.isPinned);
        _logger.i(
          'Successfully ${params.isPinned ? 'pinned' : 'unpinned'} activity',
        );

        // Invalidate relevant providers to refresh data
        ref.invalidate(classroomActivitiesProvider(params.classroomId));
        ref.invalidate(recentClassroomActivitiesProvider(params.classroomId));
        ref.invalidate(activityDetailProvider(params.activityId));
        ref.invalidate(pinnedActivitiesProvider(params.classroomId));
      } catch (e) {
        _logger.e('Error toggling activity pin: $e');
        rethrow;
      }
    });

/// Helper class for activity controller utilities
class ActivityControllerUtils {
  /// Invalidate all activity providers for a specific classroom
  static void invalidateClassroomActivityProviders(
    WidgetRef ref,
    String classroomId,
  ) {
    ref.invalidate(classroomActivitiesProvider(classroomId));
    ref.invalidate(recentClassroomActivitiesProvider(classroomId));
    ref.invalidate(pinnedActivitiesProvider(classroomId));
    _logger.i('All activity providers invalidated for classroom $classroomId');
  }

  /// Clear all activity filters
  static void clearAllActivityFilters(WidgetRef ref) {
    ref.read(activityFilterProvider.notifier).state = null;
    ref.read(activitySearchProvider.notifier).state = '';
    _logger.i('All activity filters cleared');
  }

  /// Create a new announcement activity
  static ActivityModel createAnnouncementActivity({
    required String classroomId,
    required String title,
    required String description,
    required String createdById,
    required String createdByName,
    bool isPinned = false,
  }) {
    return ActivityModel(
      id: DateTime.now().millisecondsSinceEpoch
          .toString(), // Simple ID generation
      classroomId: classroomId,
      type: ActivityType.announcement,
      title: title,
      description: description,
      createdById: createdById,
      createdByName: createdByName,
      createdAt: DateTime.now(),
      isPinned: isPinned,
    );
  }
}
