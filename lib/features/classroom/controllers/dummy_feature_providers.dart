import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

/// Dummy providers for future features that haven't been implemented yet
/// These return mock data and will be replaced when actual features are developed

final _logger = Logger();

/// Dummy model for notices
class NoticeModel {
  final String id;
  final String title;
  final String content;
  final String classroomId;
  final DateTime createdAt;
  final bool isImportant;

  const NoticeModel({
    required this.id,
    required this.title,
    required this.content,
    required this.classroomId,
    required this.createdAt,
    this.isImportant = false,
  });
}

/// Dummy model for quizzes
class QuizModel {
  final String id;
  final String title;
  final String description;
  final String classroomId;
  final DateTime dueDate;
  final int duration; // in minutes
  final bool isCompleted;

  const QuizModel({
    required this.id,
    required this.title,
    required this.description,
    required this.classroomId,
    required this.dueDate,
    required this.duration,
    this.isCompleted = false,
  });
}

/// Dummy model for attendance
class AttendanceModel {
  final String id;
  final String classroomId;
  final DateTime date;
  final bool isPresent;
  final String? remarks;

  const AttendanceModel({
    required this.id,
    required this.classroomId,
    required this.date,
    required this.isPresent,
    this.remarks,
  });
}

/// Dummy model for digital library resources
class DigitalLibraryResourceModel {
  final String id;
  final String title;
  final String description;
  final String classroomId;
  final String type; // 'pdf', 'video', 'link', 'note'
  final String url;
  final DateTime uploadedAt;
  final String uploadedBy;

  const DigitalLibraryResourceModel({
    required this.id,
    required this.title,
    required this.description,
    required this.classroomId,
    required this.type,
    required this.url,
    required this.uploadedAt,
    required this.uploadedBy,
  });
}

/// Dummy provider for notices in a classroom
final classroomNoticesProvider = FutureProvider.family<List<NoticeModel>, String>((
  ref,
  classroomId,
) async {
  _logger.i('Fetching dummy notices for classroom: $classroomId');
  
  // Simulate network delay
  await Future.delayed(const Duration(milliseconds: 500));
  
  // Return mock notices
  final notices = [
    NoticeModel(
      id: 'notice_1',
      title: 'Important: Exam Schedule Updated',
      content: 'The mid-term exam has been rescheduled to next Friday. Please check your timetable.',
      classroomId: classroomId,
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      isImportant: true,
    ),
    NoticeModel(
      id: 'notice_2',
      title: 'Library Books Due',
      content: 'Please return all borrowed library books by the end of this week.',
      classroomId: classroomId,
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
    NoticeModel(
      id: 'notice_3',
      title: 'Field Trip Permission Forms',
      content: 'Permission forms for the upcoming field trip are available at the office.',
      classroomId: classroomId,
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
  ];
  
  _logger.i('Successfully fetched ${notices.length} dummy notices');
  return notices;
});

/// Dummy provider for quizzes in a classroom
final classroomQuizzesProvider = FutureProvider.family<List<QuizModel>, String>((
  ref,
  classroomId,
) async {
  _logger.i('Fetching dummy quizzes for classroom: $classroomId');
  
  // Simulate network delay
  await Future.delayed(const Duration(milliseconds: 500));
  
  // Return mock quizzes
  final quizzes = [
    QuizModel(
      id: 'quiz_1',
      title: 'Chapter 5: Algebra Quiz',
      description: 'A quick quiz on algebraic expressions and equations.',
      classroomId: classroomId,
      dueDate: DateTime.now().add(const Duration(days: 3)),
      duration: 30,
    ),
    QuizModel(
      id: 'quiz_2',
      title: 'History: World War II',
      description: 'Test your knowledge about World War II events and timeline.',
      classroomId: classroomId,
      dueDate: DateTime.now().add(const Duration(days: 7)),
      duration: 45,
      isCompleted: true,
    ),
    QuizModel(
      id: 'quiz_3',
      title: 'Science: Chemical Reactions',
      description: 'Quiz on types of chemical reactions and their properties.',
      classroomId: classroomId,
      dueDate: DateTime.now().add(const Duration(days: 10)),
      duration: 25,
    ),
  ];
  
  _logger.i('Successfully fetched ${quizzes.length} dummy quizzes');
  return quizzes;
});

/// Dummy provider for attendance in a classroom
final classroomAttendanceProvider = FutureProvider.family<List<AttendanceModel>, String>((
  ref,
  classroomId,
) async {
  _logger.i('Fetching dummy attendance for classroom: $classroomId');
  
  // Simulate network delay
  await Future.delayed(const Duration(milliseconds: 500));
  
  // Return mock attendance for the last 7 days
  final attendance = List.generate(7, (index) {
    final date = DateTime.now().subtract(Duration(days: index));
    return AttendanceModel(
      id: 'attendance_${index + 1}',
      classroomId: classroomId,
      date: date,
      isPresent: index != 2, // Mark one day as absent
      remarks: index == 2 ? 'Sick leave' : null,
    );
  });
  
  _logger.i('Successfully fetched ${attendance.length} dummy attendance records');
  return attendance;
});

/// Dummy provider for digital library resources in a classroom
final classroomDigitalLibraryProvider = FutureProvider.family<List<DigitalLibraryResourceModel>, String>((
  ref,
  classroomId,
) async {
  _logger.i('Fetching dummy digital library resources for classroom: $classroomId');
  
  // Simulate network delay
  await Future.delayed(const Duration(milliseconds: 500));
  
  // Return mock resources
  final resources = [
    DigitalLibraryResourceModel(
      id: 'resource_1',
      title: 'Chapter 5 Notes - Algebra',
      description: 'Comprehensive notes on algebraic expressions and equations.',
      classroomId: classroomId,
      type: 'pdf',
      url: 'https://example.com/algebra-notes.pdf',
      uploadedAt: DateTime.now().subtract(const Duration(days: 2)),
      uploadedBy: 'Mr. Smith',
    ),
    DigitalLibraryResourceModel(
      id: 'resource_2',
      title: 'Video: Solving Quadratic Equations',
      description: 'Step-by-step video tutorial on solving quadratic equations.',
      classroomId: classroomId,
      type: 'video',
      url: 'https://example.com/quadratic-video',
      uploadedAt: DateTime.now().subtract(const Duration(days: 5)),
      uploadedBy: 'Mr. Smith',
    ),
    DigitalLibraryResourceModel(
      id: 'resource_3',
      title: 'Practice Problems Set 1',
      description: 'Additional practice problems for algebra concepts.',
      classroomId: classroomId,
      type: 'pdf',
      url: 'https://example.com/practice-problems.pdf',
      uploadedAt: DateTime.now().subtract(const Duration(days: 7)),
      uploadedBy: 'Mr. Smith',
    ),
    DigitalLibraryResourceModel(
      id: 'resource_4',
      title: 'Khan Academy - Algebra Basics',
      description: 'External link to Khan Academy algebra course.',
      classroomId: classroomId,
      type: 'link',
      url: 'https://khanacademy.org/algebra-basics',
      uploadedAt: DateTime.now().subtract(const Duration(days: 10)),
      uploadedBy: 'Mr. Smith',
    ),
  ];
  
  _logger.i('Successfully fetched ${resources.length} dummy digital library resources');
  return resources;
});

/// Helper class for dummy feature utilities
class DummyFeatureUtils {
  /// Show a coming soon message for features not yet implemented
  static void showComingSoonMessage(String featureName) {
    _logger.i('$featureName feature coming soon - showing dummy data');
  }
  
  /// Get count of items for a feature (for UI display)
  static int getItemCount(List<dynamic> items) {
    return items.length;
  }
}
