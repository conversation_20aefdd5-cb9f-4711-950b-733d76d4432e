import '../../../core/enums/auth_enums.dart';

/// Model representing a profile update request
class ProfileUpdateModel {
  /// User's full name
  final String? fullName;
  
  /// Type of user (student, parent, teacher, admin, other)
  final UserType? userType;
  
  /// List of class IDs the user is enrolled in or associated with
  final List<String>? classIds;
  
  /// Profile image URL
  final String? profileImageUrl;
  
  /// User's grade level (for students)
  final String? grade;
  
  /// User's school name
  final String? school;
  
  /// Student ID (for students)
  final String? studentId;
  
  /// User's bio or description
  final String? bio;
  
  /// List of subjects the user is associated with
  final List<String>? subjects;
  
  /// User's phone number
  final String? phoneNumber;
  
  /// User's date of birth
  final DateTime? dateOfBirth;
  
  /// User's address
  final String? address;
  
  /// Emergency contact information
  final String? emergencyContact;
  
  /// Emergency contact phone number
  final String? emergencyContactPhone;
  
  /// Parent/Guardian information (for students)
  final String? parentGuardianName;
  
  /// Parent/Guardian phone number (for students)
  final String? parentGuardianPhone;
  
  /// Parent/Guardian email (for students)
  final String? parentGuardianEmail;
  
  /// Whether the profile is active
  final bool? isActive;
  
  /// Additional profile metadata
  final Map<String, dynamic>? metadata;

  const ProfileUpdateModel({
    this.fullName,
    this.userType,
    this.classIds,
    this.profileImageUrl,
    this.grade,
    this.school,
    this.studentId,
    this.bio,
    this.subjects,
    this.phoneNumber,
    this.dateOfBirth,
    this.address,
    this.emergencyContact,
    this.emergencyContactPhone,
    this.parentGuardianName,
    this.parentGuardianPhone,
    this.parentGuardianEmail,
    this.isActive,
    this.metadata,
  });

  /// Create a ProfileUpdateModel from JSON
  factory ProfileUpdateModel.fromJson(Map<String, dynamic> json) {
    return ProfileUpdateModel(
      fullName: json['fullName'] as String?,
      userType: json['userType'] != null 
          ? UserTypeExtension.fromString(json['userType'] as String)
          : null,
      classIds: json['classIds'] != null 
          ? List<String>.from(json['classIds'] as List)
          : null,
      profileImageUrl: json['profileImageUrl'] as String?,
      grade: json['grade'] as String?,
      school: json['school'] as String?,
      studentId: json['studentId'] as String?,
      bio: json['bio'] as String?,
      subjects: json['subjects'] != null 
          ? List<String>.from(json['subjects'] as List)
          : null,
      phoneNumber: json['phoneNumber'] as String?,
      dateOfBirth: json['dateOfBirth'] != null 
          ? DateTime.parse(json['dateOfBirth'] as String)
          : null,
      address: json['address'] as String?,
      emergencyContact: json['emergencyContact'] as String?,
      emergencyContactPhone: json['emergencyContactPhone'] as String?,
      parentGuardianName: json['parentGuardianName'] as String?,
      parentGuardianPhone: json['parentGuardianPhone'] as String?,
      parentGuardianEmail: json['parentGuardianEmail'] as String?,
      isActive: json['isActive'] as bool?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert ProfileUpdateModel to JSON (only includes non-null fields)
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    
    if (fullName != null) json['fullName'] = fullName;
    if (userType != null) json['userType'] = userType!.value;
    if (classIds != null) json['classIds'] = classIds;
    if (profileImageUrl != null) json['profileImageUrl'] = profileImageUrl;
    if (grade != null) json['grade'] = grade;
    if (school != null) json['school'] = school;
    if (studentId != null) json['studentId'] = studentId;
    if (bio != null) json['bio'] = bio;
    if (subjects != null) json['subjects'] = subjects;
    if (phoneNumber != null) json['phoneNumber'] = phoneNumber;
    if (dateOfBirth != null) json['dateOfBirth'] = dateOfBirth!.toIso8601String();
    if (address != null) json['address'] = address;
    if (emergencyContact != null) json['emergencyContact'] = emergencyContact;
    if (emergencyContactPhone != null) json['emergencyContactPhone'] = emergencyContactPhone;
    if (parentGuardianName != null) json['parentGuardianName'] = parentGuardianName;
    if (parentGuardianPhone != null) json['parentGuardianPhone'] = parentGuardianPhone;
    if (parentGuardianEmail != null) json['parentGuardianEmail'] = parentGuardianEmail;
    if (isActive != null) json['isActive'] = isActive;
    if (metadata != null) json['metadata'] = metadata;
    
    // Always include updatedAt timestamp
    json['updatedAt'] = DateTime.now().toIso8601String();
    
    return json;
  }

  /// Check if this update model has any fields to update
  bool get hasUpdates {
    return fullName != null ||
        userType != null ||
        classIds != null ||
        profileImageUrl != null ||
        grade != null ||
        school != null ||
        studentId != null ||
        bio != null ||
        subjects != null ||
        phoneNumber != null ||
        dateOfBirth != null ||
        address != null ||
        emergencyContact != null ||
        emergencyContactPhone != null ||
        parentGuardianName != null ||
        parentGuardianPhone != null ||
        parentGuardianEmail != null ||
        isActive != null ||
        metadata != null;
  }

  @override
  String toString() {
    return 'ProfileUpdateModel(fullName: $fullName, userType: ${userType?.displayName}, hasUpdates: $hasUpdates)';
  }
}
