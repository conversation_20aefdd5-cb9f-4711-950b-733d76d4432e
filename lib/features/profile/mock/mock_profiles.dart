import 'package:faker/faker.dart';
import 'package:uuid/uuid.dart';

import '../../../core/enums/auth_enums.dart';
import '../models/profile_model.dart';
import '../../classroom/mock/mock_classes.dart';

const _uuid = Uuid();

/// Mock data for user profiles with comprehensive test coverage
final List<ProfileModel> mockProfilesList = _generateMockProfiles();

/// Generate comprehensive mock profile data ensuring proper distribution
List<ProfileModel> _generateMockProfiles() {
  final faker = Faker();
  final profiles = <ProfileModel>[];

  // Current user ID for testing
  const currentUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';

  // Get available classes for enrollment
  final availableClasses = mockClassesList;
  final classIds = availableClasses.map((c) => c.id).toList();

  // Define realistic data pools
  final schools = [
    'Greenwood High School',
    'Riverside Academy',
    'Oakmont Secondary School',
    'Westfield International School',
    'Maple Valley High School',
    'Sunnydale Preparatory School',
    'Pine Ridge Academy',
    'Cedar Creek High School',
  ];

  final grades = ['9', '10', '11', '12'];

  final subjects = [
    'Mathematics',
    'Physics',
    'Chemistry',
    'Biology',
    'English Literature',
    'History',
    'Geography',
    'Computer Science',
    'Art',
    'Music',
    'Physical Education',
    'Economics',
    'Psychology',
    'Environmental Science',
  ];

  // Create current user profile first
  profiles.add(
    _createCurrentUserProfile(
      currentUserId,
      faker,
      schools,
      grades,
      subjects,
      classIds,
    ),
  );

  // Generate 29 additional profiles (total 30)
  for (int i = 0; i < 29; i++) {
    final userId = _uuid.v4();
    final userType = _getRandomUserType(faker, i);

    profiles.add(
      _createProfile(
        userId: userId,
        faker: faker,
        schools: schools,
        grades: grades,
        subjects: subjects,
        classIds: classIds,
        userType: userType,
        index: i,
      ),
    );
  }

  return profiles;
}

/// Create the current user's profile
ProfileModel _createCurrentUserProfile(
  String userId,
  Faker faker,
  List<String> schools,
  List<String> grades,
  List<String> subjects,
  List<String> classIds,
) {
  // Ensure current user has multiple class enrollments
  final userClassIds = _getClassEnrollments(
    faker,
    classIds,
    forceMultiple: true,
  );
  final userSubjects = _getRandomSubjects(faker, subjects, userClassIds.length);

  // Set primary class to the first enrolled class (if any)
  final primaryClassId = userClassIds.isNotEmpty ? userClassIds.first : null;

  return ProfileModel(
    id: userId,
    fullName: 'Alex Johnson', // Fixed name for current user
    email: '<EMAIL>',
    userType: UserType.student,
    primaryClassId: primaryClassId,
    profileImageUrl: null, // Will use default avatar
    grade: grades[faker.randomGenerator.integer(grades.length)],
    school: schools[0], // Assign to first school
    studentId: 'STU${userId.substring(0, 8).toUpperCase()}',
    bio:
        'Passionate student interested in technology and science. Love solving complex problems and working on creative projects.',
    subjects: userSubjects,
    phoneNumber: faker.phoneNumber.us(),
    dateOfBirth: _generateRealisticBirthDate(faker),
    address: faker.address.streetAddress(),
    emergencyContact: faker.person.name(),
    emergencyContactPhone: faker.phoneNumber.us(),
    parentGuardianName: faker.person.name(),
    parentGuardianPhone: faker.phoneNumber.us(),
    parentGuardianEmail: faker.internet.email(),
    createdAt: DateTime.now().subtract(
      Duration(days: faker.randomGenerator.integer(365)),
    ),
    updatedAt: DateTime.now().subtract(
      Duration(days: faker.randomGenerator.integer(30)),
    ),
    isActive: true,
  );
}

/// Create a profile with specified parameters
ProfileModel _createProfile({
  required String userId,
  required Faker faker,
  required List<String> schools,
  required List<String> grades,
  required List<String> subjects,
  required List<String> classIds,
  required UserType userType,
  required int index,
}) {
  // Determine class enrollments based on requirements
  // 90% should have at least one class, 50% should have multiple
  final shouldHaveClasses = index < 26; // 90% of 29 = 26.1
  final shouldHaveMultiple = index < 14; // 50% of 29 = 14.5

  final userClassIds = shouldHaveClasses
      ? _getClassEnrollments(faker, classIds, forceMultiple: shouldHaveMultiple)
      : <String>[];

  final userSubjects = _getRandomSubjects(faker, subjects, userClassIds.length);

  // Set primary class for students (if they have enrollments)
  final primaryClassId = userType == UserType.student && userClassIds.isNotEmpty
      ? userClassIds.first
      : null;

  // Generate realistic profile image URL (placeholder or null)
  final profileImageUrl = faker.randomGenerator.boolean()
      ? null
      : 'https://via.placeholder.com/150x150/4285F4/FFFFFF?text=${faker.person.firstName()[0]}';

  return ProfileModel(
    id: userId,
    fullName: faker.person.name(),
    email: _generateRealisticEmail(faker, userType),
    userType: userType,
    primaryClassId: primaryClassId,
    profileImageUrl: profileImageUrl,
    grade: userType == UserType.student
        ? grades[faker.randomGenerator.integer(grades.length)]
        : null,
    school: schools[faker.randomGenerator.integer(schools.length)],
    studentId: userType == UserType.student
        ? 'STU${userId.substring(0, 8).toUpperCase()}'
        : null,
    bio: _generateRealisticBio(faker, userType),
    subjects: userSubjects,
    phoneNumber: faker.randomGenerator.boolean()
        ? faker.phoneNumber.us()
        : null,
    dateOfBirth: _generateRealisticBirthDate(faker),
    address: faker.randomGenerator.boolean()
        ? faker.address.streetAddress()
        : null,
    emergencyContact: faker.randomGenerator.boolean()
        ? faker.person.name()
        : null,
    emergencyContactPhone: faker.randomGenerator.boolean()
        ? faker.phoneNumber.us()
        : null,
    parentGuardianName:
        userType == UserType.student && faker.randomGenerator.boolean()
        ? faker.person.name()
        : null,
    parentGuardianPhone:
        userType == UserType.student && faker.randomGenerator.boolean()
        ? faker.phoneNumber.us()
        : null,
    parentGuardianEmail:
        userType == UserType.student && faker.randomGenerator.boolean()
        ? faker.internet.email()
        : null,
    createdAt: DateTime.now().subtract(
      Duration(days: faker.randomGenerator.integer(365)),
    ),
    updatedAt: DateTime.now().subtract(
      Duration(days: faker.randomGenerator.integer(30)),
    ),
    isActive: true,
  );
}

/// Get random user type with realistic distribution
UserType _getRandomUserType(Faker faker, int index) {
  // 70% students, 15% teachers, 10% parents, 3% admin, 2% other
  final random = faker.randomGenerator.integer(100);

  if (random < 70) return UserType.student;
  if (random < 85) return UserType.teacher;
  if (random < 95) return UserType.parent;
  if (random < 98) return UserType.admin;
  return UserType.other;
}

/// Get class enrollments based on requirements
List<String> _getClassEnrollments(
  Faker faker,
  List<String> classIds, {
  bool forceMultiple = false,
}) {
  if (classIds.isEmpty) return [];

  final enrollmentCount = forceMultiple
      ? faker.randomGenerator.integer(3) +
            2 // 2-4 classes
      : faker.randomGenerator.integer(4) + 1; // 1-4 classes

  final shuffled = List<String>.from(classIds)..shuffle();
  return shuffled.take(enrollmentCount.clamp(1, classIds.length)).toList();
}

/// Get random subjects based on class count
List<String> _getRandomSubjects(
  Faker faker,
  List<String> subjects,
  int classCount,
) {
  if (classCount == 0) return [];

  final subjectCount = (classCount + faker.randomGenerator.integer(3)).clamp(
    1,
    subjects.length,
  );
  final shuffled = List<String>.from(subjects)..shuffle();
  return shuffled.take(subjectCount).toList();
}

/// Generate realistic email based on user type
String _generateRealisticEmail(Faker faker, UserType userType) {
  final firstName = faker.person.firstName().toLowerCase();
  final lastName = faker.person.lastName().toLowerCase();

  switch (userType) {
    case UserType.student:
      return '$firstName.$<EMAIL>';
    case UserType.teacher:
      return '$firstName.$<EMAIL>';
    case UserType.parent:
      return '$firstName.$<EMAIL>';
    case UserType.admin:
      return '$firstName.$<EMAIL>';
    case UserType.other:
      return '$firstName.$<EMAIL>';
  }
}

/// Generate realistic bio based on user type
String? _generateRealisticBio(Faker faker, UserType userType) {
  if (!faker.randomGenerator.boolean()) return null; // 50% chance of having bio

  final bios = {
    UserType.student: [
      'Passionate about learning and exploring new subjects. Love participating in extracurricular activities.',
      'Aspiring engineer with interests in mathematics and science. Enjoy problem-solving and creative thinking.',
      'Active student involved in sports and academic clubs. Always eager to take on new challenges.',
      'Creative individual with interests in arts and literature. Enjoy expressing ideas through various mediums.',
    ],
    UserType.teacher: [
      'Dedicated educator with years of experience in fostering student growth and academic excellence.',
      'Passionate about making learning engaging and accessible for all students.',
      'Experienced teacher committed to innovative teaching methods and student success.',
      'Enthusiastic educator focused on creating inclusive and supportive learning environments.',
    ],
    UserType.parent: [
      'Supportive parent actively involved in my child\'s educational journey.',
      'Committed to working with teachers to ensure the best learning experience for my child.',
      'Engaged parent who values education and continuous learning.',
    ],
    UserType.admin: [
      'Educational administrator focused on creating excellent learning environments.',
      'Committed to supporting both students and faculty in achieving their goals.',
    ],
    UserType.other: [
      'Member of the school community dedicated to supporting educational excellence.',
    ],
  };

  final userBios = bios[userType] ?? ['Active member of the school community.'];
  return userBios[faker.randomGenerator.integer(userBios.length)];
}

/// Generate realistic birth date based on user type
DateTime? _generateRealisticBirthDate(Faker faker) {
  if (!faker.randomGenerator.boolean()) {
    return null; // 50% chance of having birth date
  }

  final now = DateTime.now();
  final yearsAgo = faker.randomGenerator.integer(50) + 15; // 15-65 years old
  return DateTime(
    now.year - yearsAgo,
    faker.randomGenerator.integer(12) + 1,
    faker.randomGenerator.integer(28) + 1,
  );
}
