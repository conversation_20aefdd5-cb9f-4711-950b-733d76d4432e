import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Header widget for authentication screens
class AuthHeader extends StatelessWidget {
  /// Main title text
  final String title;
  
  /// Subtitle text
  final String? subtitle;
  
  /// Optional logo widget
  final Widget? logo;
  
  /// Whether to show the app logo
  final bool showLogo;

  const AuthHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.logo,
    this.showLogo = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      children: [
        // Logo section
        if (showLogo) ...[
          _buildLogo(theme, colorScheme),
          SizedBox(height: 32.h),
        ],
        
        // Title
        Text(
          title,
          style: theme.textTheme.headlineMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        
        // Subtitle
        if (subtitle != null) ...[
          SizedBox(height: 8.h),
          Text(
            subtitle!,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  Widget _buildLogo(ThemeData theme, ColorScheme colorScheme) {
    if (logo != null) {
      return logo!;
    }

    // Default logo - you can replace this with your app's actual logo
    return Container(
      width: 80.w,
      height: 80.w,
      decoration: BoxDecoration(
        color: colorScheme.primary,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Icon(
        Icons.school,
        size: 40.w,
        color: colorScheme.onPrimary,
      ),
    );
  }
}
