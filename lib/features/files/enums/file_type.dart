/// Enum representing different file types for the file viewer system
enum FileType {
  /// Image files (JPG, PNG, GIF, WebP, etc.)
  image,

  /// PDF documents
  pdf,

  /// Video files (MP4, MOV, AVI, etc.)
  video,

  /// Document files (DOC, DOCX, TXT, RTF, etc.)
  document,

  /// Audio files (MP3, WAV, AAC, etc.)
  audio,

  /// Archive files (ZIP, RAR, 7Z, etc.)
  archive,

  /// Other/unknown file types
  other,
}

/// Extension to provide additional functionality for FileType
extension FileTypeExtension on FileType {
  /// Returns a human-readable label for the file type
  String get label {
    switch (this) {
      case FileType.image:
        return 'Image';
      case FileType.pdf:
        return 'PDF';
      case FileType.video:
        return 'Video';
      case FileType.document:
        return 'Document';
      case FileType.audio:
        return 'Audio';
      case FileType.archive:
        return 'Archive';
      case FileType.other:
        return 'File';
    }
  }

  /// Returns the primary color hex for the file type
  String get colorHex {
    switch (this) {
      case FileType.image:
        return '#4CAF50'; // Green
      case FileType.pdf:
        return '#F44336'; // Red
      case FileType.video:
        return '#FF5722'; // Deep Orange
      case FileType.document:
        return '#2196F3'; // Blue
      case FileType.audio:
        return '#9C27B0'; // Purple
      case FileType.archive:
        return '#FF9800'; // Orange
      case FileType.other:
        return '#9E9E9E'; // Grey
    }
  }

  /// Returns whether this file type can be viewed natively in the app
  bool get canViewNatively {
    switch (this) {
      case FileType.image:
      case FileType.pdf:
        return true;
      case FileType.video:
      case FileType.document:
      case FileType.audio:
      case FileType.archive:
      case FileType.other:
        return false;
    }
  }

  /// Returns the list of file extensions for this file type
  List<String> get extensions {
    switch (this) {
      case FileType.image:
        return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];
      case FileType.pdf:
        return ['pdf'];
      case FileType.video:
        return ['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm'];
      case FileType.document:
        return ['doc', 'docx', 'txt', 'rtf', 'odt', 'pages', 'ppt', 'pptx'];
      case FileType.audio:
        return ['mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a'];
      case FileType.archive:
        return ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'];
      case FileType.other:
        return [];
    }
  }
}

/// Utility class for file type detection
class FileTypeDetector {
  /// Detect file type from file extension
  static FileType fromExtension(String extension) {
    final ext = extension.toLowerCase();

    for (final fileType in FileType.values) {
      if (fileType.extensions.contains(ext)) {
        return fileType;
      }
    }

    return FileType.other;
  }

  /// Detect file type from file name
  static FileType fromFileName(String fileName) {
    final parts = fileName.split('.');
    if (parts.length < 2) return FileType.other;

    final extension = parts.last;
    return fromExtension(extension);
  }

  /// Detect file type from file path or URL
  static FileType fromPath(String path) {
    final fileName = path.split('/').last;
    return fromFileName(fileName);
  }
}
