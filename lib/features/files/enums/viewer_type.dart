/// Enum representing different viewer types for the file viewer system
enum ViewerType {
  /// Native image viewer with zoom and pan capabilities
  image,
  
  /// Native PDF viewer
  pdf,
  
  /// Web view for online content
  webView,
  
  /// Placeholder viewer for unsupported file types
  placeholder,
}

/// Extension to provide additional functionality for ViewerType
extension ViewerTypeExtension on ViewerType {
  /// Returns a human-readable label for the viewer type
  String get label {
    switch (this) {
      case ViewerType.image:
        return 'Image Viewer';
      case ViewerType.pdf:
        return 'PDF Viewer';
      case ViewerType.webView:
        return 'Web Viewer';
      case ViewerType.placeholder:
        return 'File Preview';
    }
  }

  /// Returns whether this viewer supports full-screen mode
  bool get supportsFullScreen {
    switch (this) {
      case ViewerType.image:
      case ViewerType.pdf:
        return true;
      case ViewerType.webView:
      case ViewerType.placeholder:
        return false;
    }
  }

  /// Returns whether this viewer supports zoom functionality
  bool get supportsZoom {
    switch (this) {
      case ViewerType.image:
        return true;
      case ViewerType.pdf:
      case ViewerType.webView:
      case ViewerType.placeholder:
        return false;
    }
  }

  /// Returns whether this viewer supports sharing
  bool get supportsSharing {
    switch (this) {
      case ViewerType.image:
      case ViewerType.pdf:
        return true;
      case ViewerType.webView:
      case ViewerType.placeholder:
        return false;
    }
  }
}
