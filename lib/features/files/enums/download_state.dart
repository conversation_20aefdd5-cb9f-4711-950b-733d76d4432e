/// Enum representing different download states for files
enum DownloadState {
  /// File is not downloaded and download hasn't started
  notDownloaded,
  
  /// Download is in progress
  downloading,
  
  /// File has been successfully downloaded
  downloaded,
  
  /// Download failed due to an error
  failed,
  
  /// Download was cancelled by the user
  cancelled,
  
  /// Download is paused
  paused,
}

/// Extension to provide additional functionality for DownloadState
extension DownloadStateExtension on DownloadState {
  /// Returns a human-readable label for the download state
  String get label {
    switch (this) {
      case DownloadState.notDownloaded:
        return 'Not Downloaded';
      case DownloadState.downloading:
        return 'Downloading';
      case DownloadState.downloaded:
        return 'Downloaded';
      case DownloadState.failed:
        return 'Download Failed';
      case DownloadState.cancelled:
        return 'Download Cancelled';
      case DownloadState.paused:
        return 'Download Paused';
    }
  }

  /// Returns whether the download is in progress
  bool get isInProgress {
    switch (this) {
      case DownloadState.downloading:
      case DownloadState.paused:
        return true;
      case DownloadState.notDownloaded:
      case DownloadState.downloaded:
      case DownloadState.failed:
      case DownloadState.cancelled:
        return false;
    }
  }

  /// Returns whether the download is complete
  bool get isComplete {
    return this == DownloadState.downloaded;
  }

  /// Returns whether the download has failed
  bool get hasFailed {
    switch (this) {
      case DownloadState.failed:
      case DownloadState.cancelled:
        return true;
      case DownloadState.notDownloaded:
      case DownloadState.downloading:
      case DownloadState.downloaded:
      case DownloadState.paused:
        return false;
    }
  }

  /// Returns whether the download can be retried
  bool get canRetry {
    switch (this) {
      case DownloadState.failed:
      case DownloadState.cancelled:
        return true;
      case DownloadState.notDownloaded:
      case DownloadState.downloading:
      case DownloadState.downloaded:
      case DownloadState.paused:
        return false;
    }
  }

  /// Returns whether the download can be paused
  bool get canPause {
    return this == DownloadState.downloading;
  }

  /// Returns whether the download can be resumed
  bool get canResume {
    return this == DownloadState.paused;
  }

  /// Returns whether the download can be cancelled
  bool get canCancel {
    switch (this) {
      case DownloadState.downloading:
      case DownloadState.paused:
        return true;
      case DownloadState.notDownloaded:
      case DownloadState.downloaded:
      case DownloadState.failed:
      case DownloadState.cancelled:
        return false;
    }
  }
}
