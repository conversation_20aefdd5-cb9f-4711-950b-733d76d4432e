import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../models/download_progress_model.dart';
import '../enums/download_state.dart';

/// Widget for managing multiple downloads
class DownloadManagerWidget extends StatelessWidget {
  /// List of active downloads
  final List<DownloadProgressModel> downloads;

  /// Map of file names for each download ID
  final Map<String, String> fileNames;

  /// Callback when a download is cancelled
  final void Function(String downloadId)? onCancelDownload;

  /// Callback when a download is retried
  final void Function(String downloadId)? onRetryDownload;

  /// Callback when a download is removed from the list
  final void Function(String downloadId)? onRemoveDownload;

  const DownloadManagerWidget({
    super.key,
    required this.downloads,
    required this.fileNames,
    this.onCancelDownload,
    this.onRetryDownload,
    this.onRemoveDownload,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (downloads.isEmpty) {
      return _buildEmptyState(theme);
    }

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(Symbols.download, size: 20.sp, color: colorScheme.primary),
                SizedBox(width: 8.w),
                Text(
                  'Downloads',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Text(
                  '${downloads.length}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),

          // Downloads list
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: downloads.length,
            separatorBuilder: (context, index) => Divider(
              height: 1,
              color: colorScheme.outline.withValues(alpha: 0.1),
            ),
            itemBuilder: (context, index) {
              final download = downloads[index];
              final fileName = fileNames[download.id] ?? 'Unknown file';

              return _DownloadItem(
                download: download,
                fileName: fileName,
                onCancel: onCancelDownload != null
                    ? () => onCancelDownload!(download.id)
                    : null,
                onRetry: onRetryDownload != null
                    ? () => onRetryDownload!(download.id)
                    : null,
                onRemove: onRemoveDownload != null
                    ? () => onRemoveDownload!(download.id)
                    : null,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Symbols.download,
            size: 48.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          SizedBox(height: 16.h),
          Text(
            'No active downloads',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }
}

/// Individual download item widget
class _DownloadItem extends StatelessWidget {
  final DownloadProgressModel download;
  final String fileName;
  final VoidCallback? onCancel;
  final VoidCallback? onRetry;
  final VoidCallback? onRemove;

  const _DownloadItem({
    required this.download,
    required this.fileName,
    this.onCancel,
    this.onRetry,
    this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File name and status
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fileName,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      download.state.label,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: _getStatusColor(download.state),
                      ),
                    ),
                  ],
                ),
              ),
              _buildActionButtons(colorScheme),
            ],
          ),

          // Progress bar (for downloading state)
          if (download.state == DownloadState.downloading) ...[
            SizedBox(height: 12.h),
            LinearProgressIndicator(
              value: download.progress,
              backgroundColor: colorScheme.outline.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
            ),
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  download.progressPercentageString,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                Text(
                  download.downloadedSizeString,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ],

          // Error message (for failed state)
          if (download.state == DownloadState.failed &&
              download.errorMessage != null) ...[
            SizedBox(height: 8.h),
            Text(
              download.errorMessage!,
              style: theme.textTheme.bodySmall?.copyWith(color: Colors.red),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons(ColorScheme colorScheme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (download.state.canCancel && onCancel != null)
          IconButton(
            onPressed: onCancel,
            icon: Icon(Symbols.close, size: 18.sp),
            iconSize: 18.sp,
            constraints: BoxConstraints(minWidth: 32.w, minHeight: 32.h),
            tooltip: 'Cancel download',
          ),
        if (download.state.canRetry && onRetry != null)
          IconButton(
            onPressed: onRetry,
            icon: Icon(Symbols.refresh, size: 18.sp),
            iconSize: 18.sp,
            constraints: BoxConstraints(minWidth: 32.w, minHeight: 32.h),
            tooltip: 'Retry download',
          ),
        if ((download.state == DownloadState.downloaded ||
                download.state == DownloadState.failed ||
                download.state == DownloadState.cancelled) &&
            onRemove != null)
          IconButton(
            onPressed: onRemove,
            icon: Icon(Symbols.delete, size: 18.sp),
            iconSize: 18.sp,
            constraints: BoxConstraints(minWidth: 32.w, minHeight: 32.h),
            tooltip: 'Remove from list',
          ),
      ],
    );
  }

  Color _getStatusColor(DownloadState state) {
    switch (state) {
      case DownloadState.downloading:
        return Colors.blue;
      case DownloadState.downloaded:
        return Colors.green;
      case DownloadState.failed:
      case DownloadState.cancelled:
        return Colors.red;
      case DownloadState.paused:
        return Colors.orange;
      case DownloadState.notDownloaded:
        return Colors.grey;
    }
  }
}
