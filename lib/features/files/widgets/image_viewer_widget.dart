import 'dart:io';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../models/file_model.dart';

/// Widget for viewing images with zoom and pan capabilities
class ImageViewerWidget extends StatefulWidget {
  /// The file model containing the image to view
  final FileModel fileModel;

  const ImageViewerWidget({super.key, required this.fileModel});

  @override
  State<ImageViewerWidget> createState() => _ImageViewerWidgetState();
}

class _ImageViewerWidgetState extends State<ImageViewerWidget> {
  late PhotoViewController _photoViewController;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _photoViewController = PhotoViewController();
  }

  @override
  void dispose() {
    _photoViewController.dispose();
    super.dispose();
  }

  /// Build image provider based on file type (local or remote)
  ImageProvider _buildImageProvider() {
    if (widget.fileModel.isLocal && widget.fileModel.localPath != null) {
      return FileImage(File(widget.fileModel.localPath!));
    } else if (widget.fileModel.url != null) {
      return CachedNetworkImageProvider(widget.fileModel.url!);
    } else {
      throw Exception('No valid image source available');
    }
  }

  /// Handle image load error
  void _onImageError(Object error) {
    if (mounted) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load image: $error';
      });
    }
  }

  /// Reset zoom to fit screen
  void _resetZoom() {
    _photoViewController.reset();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main image viewer
        PhotoView(
          imageProvider: _buildImageProvider(),
          controller: _photoViewController,
          backgroundDecoration: const BoxDecoration(color: Colors.black),
          minScale: PhotoViewComputedScale.contained,
          maxScale: PhotoViewComputedScale.covered * 3.0,
          initialScale: PhotoViewComputedScale.contained,
          heroAttributes: PhotoViewHeroAttributes(tag: widget.fileModel.id),
          loadingBuilder: (context, event) {
            _isLoading = true;
            return const Center(
              child: CircularProgressIndicator(color: Colors.white),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            _onImageError(error);
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Symbols.broken_image,
                    size: 64,
                    color: Colors.white54,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load image',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyLarge?.copyWith(color: Colors.white54),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.fileModel.fileName,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.white38),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          },
          onTapUp: (context, details, controllerValue) {
            // Handle tap to show/hide UI controls if needed
          },
        ),

        // Loading overlay
        if (_isLoading)
          Container(
            color: Colors.black54,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: Colors.white),
                  SizedBox(height: 16),
                  Text(
                    'Loading image...',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          ),

        // Error overlay
        if (_errorMessage != null)
          Container(
            color: Colors.black54,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Symbols.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _isLoading = true;
                        _errorMessage = null;
                      });
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          ),

        // Zoom controls
        Positioned(
          bottom: 32,
          right: 16,
          child: Column(
            children: [
              FloatingActionButton.small(
                heroTag: 'zoom_in',
                onPressed: () {
                  _photoViewController.scale =
                      (_photoViewController.scale ?? 1.0) * 1.5;
                },
                backgroundColor: Colors.black54,
                foregroundColor: Colors.white,
                child: const Icon(Symbols.zoom_in),
              ),
              const SizedBox(height: 8),
              FloatingActionButton.small(
                heroTag: 'zoom_out',
                onPressed: () {
                  _photoViewController.scale =
                      (_photoViewController.scale ?? 1.0) / 1.5;
                },
                backgroundColor: Colors.black54,
                foregroundColor: Colors.white,
                child: const Icon(Symbols.zoom_out),
              ),
              const SizedBox(height: 8),
              FloatingActionButton.small(
                heroTag: 'zoom_reset',
                onPressed: _resetZoom,
                backgroundColor: Colors.black54,
                foregroundColor: Colors.white,
                child: const Icon(Symbols.fit_screen),
              ),
            ],
          ),
        ),

        // File info overlay (bottom left)
        Positioned(
          bottom: 32,
          left: 16,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.fileModel.fileName,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (widget.fileModel.fileSizeBytes != null)
                  Text(
                    widget.fileModel.fileSizeString,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.white70),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
