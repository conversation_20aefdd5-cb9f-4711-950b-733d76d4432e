import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/file_model.dart';

/// Service for managing file caching
class FileCacheService {
  static final FileCacheService _instance = FileCacheService._internal();
  factory FileCacheService() => _instance;
  FileCacheService._internal();

  final Logger _logger = Logger();
  static const String _cacheMetadataKey = 'file_cache_metadata';
  static const String _cacheDirectoryName = 'file_cache';

  Directory? _cacheDirectory;
  Map<String, CacheEntry> _cacheMetadata = {};

  /// Initialize the cache service
  Future<void> initialize() async {
    try {
      _cacheDirectory = await _getCacheDirectory();
      await _loadCacheMetadata();
      await _cleanupExpiredFiles();
      _logger.i('FileCacheService initialized');
    } catch (e) {
      _logger.e('Failed to initialize FileCacheService: $e');
    }
  }

  /// Check if a file is cached
  bool isFileCached(String url) {
    final cacheKey = _generateCacheKey(url);
    final entry = _cacheMetadata[cacheKey];

    if (entry == null) return false;

    // Check if file still exists
    final file = File(entry.localPath);
    if (!file.existsSync()) {
      _removeCacheEntry(cacheKey);
      return false;
    }

    // Check if file is expired
    if (_isExpired(entry)) {
      _removeCacheEntry(cacheKey);
      return false;
    }

    return true;
  }

  /// Get cached file path
  String? getCachedFilePath(String url) {
    if (!isFileCached(url)) return null;

    final cacheKey = _generateCacheKey(url);
    final entry = _cacheMetadata[cacheKey];
    return entry?.localPath;
  }

  /// Cache a downloaded file
  Future<String?> cacheFile(FileModel fileModel) async {
    if (_cacheDirectory == null || fileModel.url == null) return null;

    try {
      final cacheKey = _generateCacheKey(fileModel.url!);
      final fileName = _generateCacheFileName(fileModel.fileName);
      final cachedFilePath = '${_cacheDirectory!.path}/$fileName';

      // If file has a local path, copy it to cache
      if (fileModel.localPath != null) {
        final sourceFile = File(fileModel.localPath!);
        if (await sourceFile.exists()) {
          await sourceFile.copy(cachedFilePath);
        } else {
          return null;
        }
      } else {
        return null;
      }

      // Create cache entry
      final entry = CacheEntry(
        url: fileModel.url!,
        localPath: cachedFilePath,
        fileName: fileModel.fileName,
        fileSize: fileModel.fileSizeBytes ?? 0,
        cachedAt: DateTime.now(),
        lastAccessed: DateTime.now(),
        expiresAt: DateTime.now().add(
          const Duration(days: 30),
        ), // 30 days default
      );

      _cacheMetadata[cacheKey] = entry;
      await _saveCacheMetadata();

      _logger.i('File cached: ${fileModel.fileName}');
      return cachedFilePath;
    } catch (e) {
      _logger.e('Failed to cache file: $e');
      return null;
    }
  }

  /// Update last accessed time for a cached file
  Future<void> updateLastAccessed(String url) async {
    final cacheKey = _generateCacheKey(url);
    final entry = _cacheMetadata[cacheKey];

    if (entry != null) {
      _cacheMetadata[cacheKey] = entry.copyWith(lastAccessed: DateTime.now());
      await _saveCacheMetadata();
    }
  }

  /// Get cache statistics
  CacheStats getCacheStats() {
    int totalFiles = _cacheMetadata.length;
    int totalSize = 0;
    int expiredFiles = 0;

    for (final entry in _cacheMetadata.values) {
      totalSize += entry.fileSize;
      if (_isExpired(entry)) {
        expiredFiles++;
      }
    }

    return CacheStats(
      totalFiles: totalFiles,
      totalSizeBytes: totalSize,
      expiredFiles: expiredFiles,
      cacheDirectory: _cacheDirectory?.path,
    );
  }

  /// Clear all cached files
  Future<void> clearCache() async {
    try {
      if (_cacheDirectory != null && await _cacheDirectory!.exists()) {
        await _cacheDirectory!.delete(recursive: true);
        await _cacheDirectory!.create();
      }

      _cacheMetadata.clear();
      await _saveCacheMetadata();

      _logger.i('Cache cleared');
    } catch (e) {
      _logger.e('Failed to clear cache: $e');
    }
  }

  /// Remove specific file from cache
  Future<void> removeFromCache(String url) async {
    final cacheKey = _generateCacheKey(url);
    await _removeCacheEntry(cacheKey);
  }

  /// Cleanup expired files
  Future<void> cleanupExpiredFiles() async {
    await _cleanupExpiredFiles();
  }

  /// Get cache directory
  Future<Directory> _getCacheDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final cacheDir = Directory('${appDir.path}/$_cacheDirectoryName');

    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }

    return cacheDir;
  }

  /// Generate cache key from URL
  String _generateCacheKey(String url) {
    return url.hashCode.toString();
  }

  /// Generate unique cache file name
  String _generateCacheFileName(String originalFileName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = originalFileName.split('.').last;
    return '${timestamp}_${originalFileName.hashCode}.$extension';
  }

  /// Check if cache entry is expired
  bool _isExpired(CacheEntry entry) {
    return DateTime.now().isAfter(entry.expiresAt);
  }

  /// Load cache metadata from shared preferences
  Future<void> _loadCacheMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metadataJson = prefs.getString(_cacheMetadataKey);

      if (metadataJson != null) {
        final Map<String, dynamic> data = jsonDecode(metadataJson);
        _cacheMetadata = data.map(
          (key, value) => MapEntry(key, CacheEntry.fromJson(value)),
        );
      }
    } catch (e) {
      _logger.e('Failed to load cache metadata: $e');
      _cacheMetadata = {};
    }
  }

  /// Save cache metadata to shared preferences
  Future<void> _saveCacheMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = _cacheMetadata.map(
        (key, value) => MapEntry(key, value.toJson()),
      );
      await prefs.setString(_cacheMetadataKey, jsonEncode(data));
    } catch (e) {
      _logger.e('Failed to save cache metadata: $e');
    }
  }

  /// Remove cache entry and delete file
  Future<void> _removeCacheEntry(String cacheKey) async {
    final entry = _cacheMetadata[cacheKey];
    if (entry != null) {
      try {
        final file = File(entry.localPath);
        if (await file.exists()) {
          await file.delete();
        }
      } catch (e) {
        _logger.e('Failed to delete cached file: $e');
      }

      _cacheMetadata.remove(cacheKey);
      await _saveCacheMetadata();
    }
  }

  /// Cleanup expired files
  Future<void> _cleanupExpiredFiles() async {
    final expiredKeys = <String>[];

    for (final entry in _cacheMetadata.entries) {
      if (_isExpired(entry.value)) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      await _removeCacheEntry(key);
    }

    if (expiredKeys.isNotEmpty) {
      _logger.i('Cleaned up ${expiredKeys.length} expired files');
    }
  }
}

/// Cache entry model
class CacheEntry {
  final String url;
  final String localPath;
  final String fileName;
  final int fileSize;
  final DateTime cachedAt;
  final DateTime lastAccessed;
  final DateTime expiresAt;

  const CacheEntry({
    required this.url,
    required this.localPath,
    required this.fileName,
    required this.fileSize,
    required this.cachedAt,
    required this.lastAccessed,
    required this.expiresAt,
  });

  CacheEntry copyWith({
    String? url,
    String? localPath,
    String? fileName,
    int? fileSize,
    DateTime? cachedAt,
    DateTime? lastAccessed,
    DateTime? expiresAt,
  }) {
    return CacheEntry(
      url: url ?? this.url,
      localPath: localPath ?? this.localPath,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      cachedAt: cachedAt ?? this.cachedAt,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'localPath': localPath,
      'fileName': fileName,
      'fileSize': fileSize,
      'cachedAt': cachedAt.toIso8601String(),
      'lastAccessed': lastAccessed.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
    };
  }

  factory CacheEntry.fromJson(Map<String, dynamic> json) {
    return CacheEntry(
      url: json['url'],
      localPath: json['localPath'],
      fileName: json['fileName'],
      fileSize: json['fileSize'],
      cachedAt: DateTime.parse(json['cachedAt']),
      lastAccessed: DateTime.parse(json['lastAccessed']),
      expiresAt: DateTime.parse(json['expiresAt']),
    );
  }
}

/// Cache statistics model
class CacheStats {
  final int totalFiles;
  final int totalSizeBytes;
  final int expiredFiles;
  final String? cacheDirectory;

  const CacheStats({
    required this.totalFiles,
    required this.totalSizeBytes,
    required this.expiredFiles,
    this.cacheDirectory,
  });

  String get totalSizeString {
    if (totalSizeBytes < 1024) return '$totalSizeBytes B';
    if (totalSizeBytes < 1024 * 1024) {
      return '${(totalSizeBytes / 1024).toStringAsFixed(1)} KB';
    }
    if (totalSizeBytes < 1024 * 1024 * 1024) {
      return '${(totalSizeBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(totalSizeBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
