// ignore_for_file: deprecated_member_use

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:logger/logger.dart';
import '../models/file_model.dart';
import '../enums/file_type.dart';

/// Service for handling file sharing operations
class FileSharingService {
  static final FileSharingService _instance = FileSharingService._internal();
  factory FileSharingService() => _instance;
  FileSharingService._internal();

  final Logger _logger = Logger();

  /// Share a file
  Future<ShareResult?> shareFile(
    FileModel fileModel, {
    String? customText,
    String? customSubject,
    Rect? sharePositionOrigin,
  }) async {
    try {
      _logger.i('Sharing file: ${fileModel.fileName}');

      if (fileModel.isLocal && fileModel.localPath != null) {
        return await _shareLocalFile(
          fileModel,
          customText: customText,
          customSubject: customSubject,
          sharePositionOrigin: sharePositionOrigin,
        );
      } else if (fileModel.url != null) {
        return await _shareRemoteFile(
          fileModel,
          customText: customText,
          customSubject: customSubject,
          sharePositionOrigin: sharePositionOrigin,
        );
      } else {
        throw Exception('No valid file path or URL to share');
      }
    } catch (e) {
      _logger.e('Error sharing file: $e');
      rethrow;
    }
  }

  /// Share multiple files
  Future<ShareResult?> shareFiles(
    List<FileModel> fileModels, {
    String? customText,
    String? customSubject,
    Rect? sharePositionOrigin,
  }) async {
    try {
      _logger.i('Sharing ${fileModels.length} files');

      final localFiles = <XFile>[];
      final urls = <String>[];

      for (final fileModel in fileModels) {
        if (fileModel.isLocal && fileModel.localPath != null) {
          localFiles.add(XFile(fileModel.localPath!));
        } else if (fileModel.url != null) {
          urls.add(fileModel.url!);
        }
      }

      if (localFiles.isNotEmpty && urls.isEmpty) {
        // Share only local files
        return await Share.shareXFiles(
          localFiles,
          text: customText ?? _generateShareText(fileModels),
          subject: customSubject ?? _generateShareSubject(fileModels),
          sharePositionOrigin: sharePositionOrigin,
        );
      } else if (urls.isNotEmpty && localFiles.isEmpty) {
        // Share only URLs
        return await Share.share(
          urls.join('\n'),
          subject: customSubject ?? _generateShareSubject(fileModels),
          sharePositionOrigin: sharePositionOrigin,
        );
      } else if (localFiles.isNotEmpty && urls.isNotEmpty) {
        // Mixed content - share files first, then URLs
        final fileResult = await Share.shareXFiles(
          localFiles,
          text: customText ?? _generateShareText(fileModels),
          subject: customSubject ?? _generateShareSubject(fileModels),
          sharePositionOrigin: sharePositionOrigin,
        );

        // Note: We can't share both files and URLs in a single share action
        // This is a limitation of the platform share APIs
        return fileResult;
      } else {
        throw Exception('No valid files to share');
      }
    } catch (e) {
      _logger.e('Error sharing files: $e');
      rethrow;
    }
  }

  /// Share file as text (URL or file info)
  Future<ShareResult?> shareFileAsText(
    FileModel fileModel, {
    String? customText,
    String? customSubject,
    Rect? sharePositionOrigin,
  }) async {
    try {
      final shareText = customText ?? _generateFileInfoText(fileModel);

      return await Share.share(
        shareText,
        subject: customSubject ?? fileModel.fileName,
        sharePositionOrigin: sharePositionOrigin,
      );
    } catch (e) {
      _logger.e('Error sharing file as text: $e');
      rethrow;
    }
  }

  /// Check if a file can be shared
  bool canShareFile(FileModel fileModel) {
    return (fileModel.isLocal && fileModel.localPath != null) ||
        (fileModel.url != null);
  }

  /// Get share options for a file
  List<ShareOption> getShareOptions(FileModel fileModel) {
    final options = <ShareOption>[];

    if (fileModel.isLocal && fileModel.localPath != null) {
      options.add(ShareOption.file);
    }

    if (fileModel.url != null) {
      options.add(ShareOption.url);
    }

    options.add(ShareOption.text);

    return options;
  }

  /// Share a local file
  Future<ShareResult?> _shareLocalFile(
    FileModel fileModel, {
    String? customText,
    String? customSubject,
    Rect? sharePositionOrigin,
  }) async {
    final file = File(fileModel.localPath!);

    if (!await file.exists()) {
      throw Exception('File does not exist: ${fileModel.localPath}');
    }

    return await Share.shareXFiles(
      [XFile(fileModel.localPath!)],
      text: customText ?? _generateShareText([fileModel]),
      subject: customSubject ?? fileModel.fileName,
      sharePositionOrigin: sharePositionOrigin,
    );
  }

  /// Share a remote file (URL)
  Future<ShareResult?> _shareRemoteFile(
    FileModel fileModel, {
    String? customText,
    String? customSubject,
    Rect? sharePositionOrigin,
  }) async {
    final shareText = customText ?? fileModel.url!;

    return await Share.share(
      shareText,
      subject: customSubject ?? fileModel.fileName,
      sharePositionOrigin: sharePositionOrigin,
    );
  }

  /// Generate share text for files
  String _generateShareText(List<FileModel> fileModels) {
    if (fileModels.length == 1) {
      return 'Sharing: ${fileModels.first.fileName}';
    } else {
      return 'Sharing ${fileModels.length} files';
    }
  }

  /// Generate share subject for files
  String _generateShareSubject(List<FileModel> fileModels) {
    if (fileModels.length == 1) {
      return fileModels.first.fileName;
    } else {
      return '${fileModels.length} files';
    }
  }

  /// Generate file info text
  String _generateFileInfoText(FileModel fileModel) {
    final buffer = StringBuffer();

    buffer.writeln('File: ${fileModel.fileName}');
    buffer.writeln('Type: ${fileModel.fileType.label}');

    if (fileModel.fileSizeBytes != null) {
      buffer.writeln('Size: ${fileModel.fileSizeString}');
    }

    if (fileModel.url != null) {
      buffer.writeln('URL: ${fileModel.url}');
    }

    if (fileModel.isLocal) {
      buffer.writeln('Status: Available offline');
    } else {
      buffer.writeln('Status: Online only');
    }

    return buffer.toString().trim();
  }
}

/// Share option types
enum ShareOption {
  /// Share the actual file
  file,

  /// Share the file URL
  url,

  /// Share file information as text
  text,
}

/// Extension for ShareOption
extension ShareOptionExtension on ShareOption {
  /// Get human-readable label
  String get label {
    switch (this) {
      case ShareOption.file:
        return 'Share File';
      case ShareOption.url:
        return 'Share Link';
      case ShareOption.text:
        return 'Share Info';
    }
  }

  /// Get icon for the share option
  IconData get icon {
    switch (this) {
      case ShareOption.file:
        return Icons.attach_file;
      case ShareOption.url:
        return Icons.link;
      case ShareOption.text:
        return Icons.text_fields;
    }
  }

  /// Get description for the share option
  String get description {
    switch (this) {
      case ShareOption.file:
        return 'Share the actual file';
      case ShareOption.url:
        return 'Share the file URL/link';
      case ShareOption.text:
        return 'Share file information as text';
    }
  }
}

/// Share options dialog helper
class ShareOptionsHelper {
  /// Show share options dialog
  static Future<ShareOption?> showShareOptionsDialog(
    BuildContext context,
    FileModel fileModel,
  ) async {
    final sharingService = FileSharingService();
    final options = sharingService.getShareOptions(fileModel);

    if (options.length == 1) {
      return options.first;
    }

    return await showDialog<ShareOption>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Options'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.map((option) {
            return ListTile(
              leading: Icon(option.icon),
              title: Text(option.label),
              subtitle: Text(option.description),
              onTap: () => Navigator.of(context).pop(option),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
