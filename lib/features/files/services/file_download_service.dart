import 'dart:io';
import 'dart:async';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';

import 'package:logger/logger.dart';
import '../models/file_model.dart';
import '../models/download_progress_model.dart';
import '../enums/download_state.dart';
import 'file_cache_service.dart';

/// Service for downloading files from URLs with progress tracking
class FileDownloadService {
  static final FileDownloadService _instance = FileDownloadService._internal();
  factory FileDownloadService() => _instance;
  FileDownloadService._internal();

  final Dio _dio = Dio();
  final Logger _logger = Logger();
  final FileCacheService _cacheService = FileCacheService();

  /// Map to track active downloads
  final Map<String, CancelToken> _activeDownloads = {};

  /// Stream controller for download progress updates
  final StreamController<DownloadProgressModel> _progressController =
      StreamController<DownloadProgressModel>.broadcast();

  /// Stream of download progress updates
  Stream<DownloadProgressModel> get progressStream =>
      _progressController.stream;

  /// Initialize the download service
  void initialize() {
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.sendTimeout = const Duration(seconds: 30);

    _logger.i('FileDownloadService initialized');
  }

  /// Download a file from URL
  Future<FileModel?> downloadFile(FileModel fileModel) async {
    if (fileModel.url == null) {
      _logger.e('Cannot download file: URL is null');
      return null;
    }

    // Check if file is already cached
    if (_cacheService.isFileCached(fileModel.url!)) {
      final cachedPath = _cacheService.getCachedFilePath(fileModel.url!);
      if (cachedPath != null) {
        _logger.i('File found in cache: ${fileModel.fileName}');
        await _cacheService.updateLastAccessed(fileModel.url!);

        return fileModel.copyWith(
          localPath: cachedPath,
          downloadState: DownloadState.downloaded,
          downloadProgress: 1.0,
        );
      }
    }

    if (_activeDownloads.containsKey(fileModel.id)) {
      _logger.w('Download already in progress for file: ${fileModel.id}');
      return null;
    }

    try {
      // Create cancel token for this download
      final cancelToken = CancelToken();
      _activeDownloads[fileModel.id] = cancelToken;

      // Emit started progress
      _emitProgress(DownloadProgressModel.started(fileModel.id));

      // Get download directory
      final downloadDir = await _getDownloadDirectory();
      if (downloadDir == null) {
        throw Exception('Could not access download directory');
      }

      // Generate unique filename to avoid conflicts
      final fileName = _generateUniqueFileName(downloadDir, fileModel.fileName);
      final filePath = '${downloadDir.path}/$fileName';

      _logger.i('Starting download: ${fileModel.url} -> $filePath');

      // Download the file
      final response = await _dio.download(
        fileModel.url!,
        filePath,
        cancelToken: cancelToken,
        onReceiveProgress: (received, total) {
          if (total > 0) {
            final progress = received / total;
            final speed = _calculateDownloadSpeed(received, total);
            final estimatedTime = _calculateEstimatedTime(
              received,
              total,
              speed,
            );

            _emitProgress(
              DownloadProgressModel(
                id: fileModel.id,
                state: DownloadState.downloading,
                progress: progress,
                downloadedBytes: received,
                totalBytes: total,
                speedBytesPerSecond: speed,
                estimatedTimeRemainingSeconds: estimatedTime,
              ),
            );
          }
        },
      );

      // Remove from active downloads
      _activeDownloads.remove(fileModel.id);

      if (response.statusCode == 200) {
        final downloadedFile = File(filePath);
        final fileSize = await downloadedFile.length();

        // Emit completed progress
        _emitProgress(DownloadProgressModel.completed(fileModel.id, fileSize));

        // Create updated file model
        final updatedFileModel = fileModel.copyWith(
          localPath: filePath,
          downloadState: DownloadState.downloaded,
          downloadProgress: 1.0,
          fileSizeBytes: fileSize,
        );

        // Cache the downloaded file
        try {
          await _cacheService.cacheFile(updatedFileModel);
          _logger.i('File cached successfully: ${fileModel.fileName}');
        } catch (e) {
          _logger.w('Failed to cache file: $e');
        }

        _logger.i('Download completed: $filePath');
        return updatedFileModel;
      } else {
        throw Exception('Download failed with status: ${response.statusCode}');
      }
    } catch (e) {
      // Remove from active downloads
      _activeDownloads.remove(fileModel.id);

      String errorMessage;
      if (e is DioException) {
        if (e.type == DioExceptionType.cancel) {
          errorMessage = 'Download cancelled';
          _emitProgress(
            DownloadProgressModel(
              id: fileModel.id,
              state: DownloadState.cancelled,
              progress: 0.0,
              downloadedBytes: 0,
              errorMessage: errorMessage,
            ),
          );
        } else {
          errorMessage = 'Network error: ${e.message}';
          _emitProgress(
            DownloadProgressModel.failed(fileModel.id, errorMessage),
          );
        }
      } else {
        errorMessage = 'Download failed: $e';
        _emitProgress(DownloadProgressModel.failed(fileModel.id, errorMessage));
      }

      _logger.e('Download failed for ${fileModel.id}: $errorMessage');
      return null;
    }
  }

  /// Cancel an active download
  Future<void> cancelDownload(String fileId) async {
    final cancelToken = _activeDownloads[fileId];
    if (cancelToken != null) {
      cancelToken.cancel('Download cancelled by user');
      _activeDownloads.remove(fileId);

      _emitProgress(
        DownloadProgressModel(
          id: fileId,
          state: DownloadState.cancelled,
          progress: 0.0,
          downloadedBytes: 0,
          errorMessage: 'Download cancelled by user',
        ),
      );

      _logger.i('Download cancelled: $fileId');
    }
  }

  /// Check if a file is currently being downloaded
  bool isDownloading(String fileId) {
    return _activeDownloads.containsKey(fileId);
  }

  /// Check if a file is cached
  bool isFileCached(String url) {
    return _cacheService.isFileCached(url);
  }

  /// Get cached file path
  String? getCachedFilePath(String url) {
    return _cacheService.getCachedFilePath(url);
  }

  /// Get the download directory
  Future<Directory?> _getDownloadDirectory() async {
    try {
      if (Platform.isAndroid) {
        // Try to get external storage directory first
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          final downloadDir = Directory('${externalDir.path}/Downloads');
          if (!await downloadDir.exists()) {
            await downloadDir.create(recursive: true);
          }
          return downloadDir;
        }
      }

      // Fallback to application documents directory
      final appDir = await getApplicationDocumentsDirectory();
      final downloadDir = Directory('${appDir.path}/Downloads');
      if (!await downloadDir.exists()) {
        await downloadDir.create(recursive: true);
      }
      return downloadDir;
    } catch (e) {
      _logger.e('Error getting download directory: $e');
      return null;
    }
  }

  /// Generate a unique filename to avoid conflicts
  String _generateUniqueFileName(Directory directory, String originalFileName) {
    final baseName = originalFileName.split('.').first;
    final extension = originalFileName.split('.').last;

    String fileName = originalFileName;
    int counter = 1;

    while (File('${directory.path}/$fileName').existsSync()) {
      fileName = '${baseName}_$counter.$extension';
      counter++;
    }

    return fileName;
  }

  /// Calculate download speed (simplified)
  double? _calculateDownloadSpeed(int received, int total) {
    // This is a simplified calculation
    // In a real implementation, you'd track time and calculate actual speed
    return null;
  }

  /// Calculate estimated time remaining (simplified)
  int? _calculateEstimatedTime(int received, int total, double? speed) {
    if (speed == null || speed <= 0) return null;

    final remaining = total - received;
    return (remaining / speed).round();
  }

  /// Emit progress update
  void _emitProgress(DownloadProgressModel progress) {
    if (!_progressController.isClosed) {
      _progressController.add(progress);
    }
  }

  /// Dispose the service
  void dispose() {
    // Cancel all active downloads
    for (final cancelToken in _activeDownloads.values) {
      cancelToken.cancel('Service disposed');
    }
    _activeDownloads.clear();

    _progressController.close();
    _dio.close();

    _logger.i('FileDownloadService disposed');
  }
}
