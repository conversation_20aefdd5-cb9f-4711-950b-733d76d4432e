import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:logger/logger.dart';

/// Service for handling file-related permissions
class FilePermissionsService {
  static final FilePermissionsService _instance =
      FilePermissionsService._internal();
  factory FilePermissionsService() => _instance;
  FilePermissionsService._internal();

  final Logger _logger = Logger();

  /// Request storage permission for file downloads
  Future<bool> requestStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        // For Android 13+ (API 33+), we need different permissions
        final androidInfo = await _getAndroidVersion();

        if (androidInfo >= 33) {
          // Android 13+ uses scoped storage, no special permission needed for app-specific directories
          return true;
        } else {
          // Android 12 and below
          final status = await Permission.storage.request();
          return status.isGranted;
        }
      } else if (Platform.isIOS) {
        // iOS doesn't require explicit storage permission for app documents
        return true;
      }

      return false;
    } catch (e) {
      _logger.e('Error requesting storage permission: $e');
      return false;
    }
  }

  /// Request photo library permission
  Future<bool> requestPhotoPermission() async {
    try {
      final status = await Permission.photos.request();
      return status.isGranted;
    } catch (e) {
      _logger.e('Error requesting photo permission: $e');
      return false;
    }
  }

  /// Request camera permission
  Future<bool> requestCameraPermission() async {
    try {
      final status = await Permission.camera.request();
      return status.isGranted;
    } catch (e) {
      _logger.e('Error requesting camera permission: $e');
      return false;
    }
  }

  /// Check if storage permission is granted
  Future<bool> hasStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _getAndroidVersion();

        if (androidInfo >= 33) {
          return true; // No explicit permission needed for scoped storage
        } else {
          final status = await Permission.storage.status;
          return status.isGranted;
        }
      } else if (Platform.isIOS) {
        return true; // iOS doesn't require explicit storage permission
      }

      return false;
    } catch (e) {
      _logger.e('Error checking storage permission: $e');
      return false;
    }
  }

  /// Check if photo permission is granted
  Future<bool> hasPhotoPermission() async {
    try {
      final status = await Permission.photos.status;
      return status.isGranted;
    } catch (e) {
      _logger.e('Error checking photo permission: $e');
      return false;
    }
  }

  /// Check if camera permission is granted
  Future<bool> hasCameraPermission() async {
    try {
      final status = await Permission.camera.status;
      return status.isGranted;
    } catch (e) {
      _logger.e('Error checking camera permission: $e');
      return false;
    }
  }

  /// Show permission denied dialog
  Future<bool> showPermissionDeniedDialog(
    BuildContext context,
    String permissionName,
    String reason,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$permissionName Permission Required'),
        content: Text(reason),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );

    if (result == true) {
      await openAppSettings();
    }

    return result ?? false;
  }

  /// Show permission rationale dialog
  Future<bool> showPermissionRationaleDialog(
    BuildContext context,
    String permissionName,
    String reason,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$permissionName Permission'),
        content: Text(reason),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Not Now'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Grant Permission'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// Handle permission request with proper flow
  Future<bool> handlePermissionRequest(
    BuildContext context,
    Permission permission,
    String permissionName,
    String reason,
  ) async {
    try {
      // Check current status
      final status = await permission.status;

      if (status.isGranted) {
        return true;
      }

      if (status.isDenied) {
        // Show rationale and request permission
        if (context.mounted) {
          final shouldRequest = await showPermissionRationaleDialog(
            context,
            permissionName,
            reason,
          );

          if (!shouldRequest) return false;
        } else {
          return false;
        }

        final newStatus = await permission.request();
        return newStatus.isGranted;
      }

      if (status.isPermanentlyDenied) {
        // Show dialog to open settings
        if (context.mounted) {
          await showPermissionDeniedDialog(
            context,
            permissionName,
            '$reason\n\nPlease enable $permissionName permission in app settings.',
          );
        }
        return false;
      }

      // For other statuses, try to request
      final newStatus = await permission.request();
      return newStatus.isGranted;
    } catch (e) {
      _logger.e('Error handling permission request: $e');
      return false;
    }
  }

  /// Get Android API level
  Future<int> _getAndroidVersion() async {
    // This is a simplified version. In a real app, you might want to use
    // device_info_plus package to get the actual Android API level
    return 30; // Default to API 30 for now
  }
}

/// File error types
enum FileErrorType {
  /// Network-related errors
  network,

  /// Permission-related errors
  permission,

  /// File system errors
  fileSystem,

  /// File format/corruption errors
  fileFormat,

  /// Storage space errors
  storage,

  /// Unknown errors
  unknown,
}

/// File error model
class FileError {
  final FileErrorType type;
  final String message;
  final String? details;
  final Exception? originalException;

  const FileError({
    required this.type,
    required this.message,
    this.details,
    this.originalException,
  });

  /// Create a network error
  factory FileError.network(
    String message, {
    String? details,
    Exception? originalException,
  }) {
    return FileError(
      type: FileErrorType.network,
      message: message,
      details: details,
      originalException: originalException,
    );
  }

  /// Create a permission error
  factory FileError.permission(
    String message, {
    String? details,
    Exception? originalException,
  }) {
    return FileError(
      type: FileErrorType.permission,
      message: message,
      details: details,
      originalException: originalException,
    );
  }

  /// Create a file system error
  factory FileError.fileSystem(
    String message, {
    String? details,
    Exception? originalException,
  }) {
    return FileError(
      type: FileErrorType.fileSystem,
      message: message,
      details: details,
      originalException: originalException,
    );
  }

  /// Create a file format error
  factory FileError.fileFormat(
    String message, {
    String? details,
    Exception? originalException,
  }) {
    return FileError(
      type: FileErrorType.fileFormat,
      message: message,
      details: details,
      originalException: originalException,
    );
  }

  /// Create a storage error
  factory FileError.storage(
    String message, {
    String? details,
    Exception? originalException,
  }) {
    return FileError(
      type: FileErrorType.storage,
      message: message,
      details: details,
      originalException: originalException,
    );
  }

  /// Create an unknown error
  factory FileError.unknown(
    String message, {
    String? details,
    Exception? originalException,
  }) {
    return FileError(
      type: FileErrorType.unknown,
      message: message,
      details: details,
      originalException: originalException,
    );
  }

  /// Get user-friendly error message
  String get userFriendlyMessage {
    switch (type) {
      case FileErrorType.network:
        return 'Network error: $message';
      case FileErrorType.permission:
        return 'Permission error: $message';
      case FileErrorType.fileSystem:
        return 'File system error: $message';
      case FileErrorType.fileFormat:
        return 'File format error: $message';
      case FileErrorType.storage:
        return 'Storage error: $message';
      case FileErrorType.unknown:
        return 'Error: $message';
    }
  }

  /// Get error icon
  IconData get icon {
    switch (type) {
      case FileErrorType.network:
        return Icons.wifi_off;
      case FileErrorType.permission:
        return Icons.lock;
      case FileErrorType.fileSystem:
        return Icons.folder_off;
      case FileErrorType.fileFormat:
        return Icons.broken_image;
      case FileErrorType.storage:
        return Icons.storage;
      case FileErrorType.unknown:
        return Icons.error;
    }
  }

  @override
  String toString() {
    return 'FileError(type: $type, message: $message, details: $details)';
  }
}

/// File error handler service
class FileErrorHandler {
  static final FileErrorHandler _instance = FileErrorHandler._internal();
  factory FileErrorHandler() => _instance;
  FileErrorHandler._internal();

  final Logger _logger = Logger();

  /// Handle and convert exceptions to FileError
  FileError handleException(Exception exception, {String? context}) {
    _logger.e(
      'File operation error${context != null ? ' in $context' : ''}: $exception',
    );

    if (exception.toString().contains('Permission denied') ||
        exception.toString().contains('Access denied')) {
      return FileError.permission(
        'Permission denied',
        details:
            'The app does not have permission to access this file or location.',
        originalException: exception,
      );
    }

    if (exception.toString().contains('No space left') ||
        exception.toString().contains('Storage full')) {
      return FileError.storage(
        'Insufficient storage space',
        details: 'There is not enough storage space available on the device.',
        originalException: exception,
      );
    }

    if (exception.toString().contains('Network') ||
        exception.toString().contains('Connection') ||
        exception.toString().contains('timeout')) {
      return FileError.network(
        'Network connection error',
        details: 'Please check your internet connection and try again.',
        originalException: exception,
      );
    }

    if (exception.toString().contains('File not found') ||
        exception.toString().contains('No such file')) {
      return FileError.fileSystem(
        'File not found',
        details: 'The requested file could not be found.',
        originalException: exception,
      );
    }

    if (exception.toString().contains('Corrupt') ||
        exception.toString().contains('Invalid format')) {
      return FileError.fileFormat(
        'Invalid file format',
        details:
            'The file appears to be corrupted or in an unsupported format.',
        originalException: exception,
      );
    }

    return FileError.unknown(
      'An unexpected error occurred',
      details: exception.toString(),
      originalException: exception,
    );
  }

  /// Show error dialog to user
  Future<void> showErrorDialog(
    BuildContext context,
    FileError error, {
    VoidCallback? onRetry,
  }) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(error.icon, color: Colors.red),
            const SizedBox(width: 8),
            const Text('Error'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(error.userFriendlyMessage),
            if (error.details != null) ...[
              const SizedBox(height: 8),
              Text(
                error.details!,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
          if (onRetry != null)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              child: const Text('Retry'),
            ),
        ],
      ),
    );
  }

  /// Show error snackbar
  void showErrorSnackbar(
    BuildContext context,
    FileError error, {
    VoidCallback? onRetry,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(error.icon, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(error.userFriendlyMessage)),
          ],
        ),
        backgroundColor: Colors.red,
        action: onRetry != null
            ? SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }
}
