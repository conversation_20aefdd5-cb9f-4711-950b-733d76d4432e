import '../enums/download_state.dart';

/// Model representing the progress of a file download
class DownloadProgressModel {
  /// Unique identifier for the download (usually matches file ID)
  final String id;
  
  /// Current download state
  final DownloadState state;
  
  /// Download progress (0.0 to 1.0)
  final double progress;
  
  /// Number of bytes downloaded
  final int downloadedBytes;
  
  /// Total number of bytes to download (null if unknown)
  final int? totalBytes;
  
  /// Download speed in bytes per second (null if unknown)
  final double? speedBytesPerSecond;
  
  /// Estimated time remaining in seconds (null if unknown)
  final int? estimatedTimeRemainingSeconds;
  
  /// Error message if download failed
  final String? errorMessage;
  
  /// When the download started
  final DateTime? startedAt;
  
  /// When the download completed (or failed)
  final DateTime? completedAt;

  const DownloadProgressModel({
    required this.id,
    required this.state,
    required this.progress,
    required this.downloadedBytes,
    this.totalBytes,
    this.speedBytesPerSecond,
    this.estimatedTimeRemainingSeconds,
    this.errorMessage,
    this.startedAt,
    this.completedAt,
  });

  /// Create an initial download progress model
  factory DownloadProgressModel.initial(String id) {
    return DownloadProgressModel(
      id: id,
      state: DownloadState.notDownloaded,
      progress: 0.0,
      downloadedBytes: 0,
    );
  }

  /// Create a download progress model for a started download
  factory DownloadProgressModel.started(String id) {
    return DownloadProgressModel(
      id: id,
      state: DownloadState.downloading,
      progress: 0.0,
      downloadedBytes: 0,
      startedAt: DateTime.now(),
    );
  }

  /// Create a download progress model for a completed download
  factory DownloadProgressModel.completed(String id, int totalBytes) {
    return DownloadProgressModel(
      id: id,
      state: DownloadState.downloaded,
      progress: 1.0,
      downloadedBytes: totalBytes,
      totalBytes: totalBytes,
      completedAt: DateTime.now(),
    );
  }

  /// Create a download progress model for a failed download
  factory DownloadProgressModel.failed(String id, String errorMessage) {
    return DownloadProgressModel(
      id: id,
      state: DownloadState.failed,
      progress: 0.0,
      downloadedBytes: 0,
      errorMessage: errorMessage,
      completedAt: DateTime.now(),
    );
  }

  /// Get the download speed as a human-readable string
  String get speedString {
    if (speedBytesPerSecond == null) return 'Unknown speed';
    return '${_formatFileSize(speedBytesPerSecond!.round())}/s';
  }

  /// Get the downloaded size as a human-readable string
  String get downloadedSizeString {
    return _formatFileSize(downloadedBytes);
  }

  /// Get the total size as a human-readable string
  String get totalSizeString {
    if (totalBytes == null) return 'Unknown size';
    return _formatFileSize(totalBytes!);
  }

  /// Get the progress as a percentage string
  String get progressPercentageString {
    return '${(progress * 100).toStringAsFixed(1)}%';
  }

  /// Get the estimated time remaining as a human-readable string
  String get estimatedTimeRemainingString {
    if (estimatedTimeRemainingSeconds == null) return 'Unknown time';
    
    final seconds = estimatedTimeRemainingSeconds!;
    if (seconds < 60) return '${seconds}s';
    if (seconds < 3600) return '${(seconds / 60).round()}m';
    return '${(seconds / 3600).round()}h';
  }

  /// Whether the download is in progress
  bool get isInProgress => state.isInProgress;

  /// Whether the download is complete
  bool get isComplete => state.isComplete;

  /// Whether the download has failed
  bool get hasFailed => state.hasFailed;

  /// Create a copy of this model with updated properties
  DownloadProgressModel copyWith({
    String? id,
    DownloadState? state,
    double? progress,
    int? downloadedBytes,
    int? totalBytes,
    double? speedBytesPerSecond,
    int? estimatedTimeRemainingSeconds,
    String? errorMessage,
    DateTime? startedAt,
    DateTime? completedAt,
  }) {
    return DownloadProgressModel(
      id: id ?? this.id,
      state: state ?? this.state,
      progress: progress ?? this.progress,
      downloadedBytes: downloadedBytes ?? this.downloadedBytes,
      totalBytes: totalBytes ?? this.totalBytes,
      speedBytesPerSecond: speedBytesPerSecond ?? this.speedBytesPerSecond,
      estimatedTimeRemainingSeconds: estimatedTimeRemainingSeconds ?? this.estimatedTimeRemainingSeconds,
      errorMessage: errorMessage ?? this.errorMessage,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  /// Format file size in bytes to human-readable string
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  @override
  String toString() {
    return 'DownloadProgressModel(id: $id, state: $state, progress: $progress, '
        'downloadedBytes: $downloadedBytes, totalBytes: $totalBytes)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DownloadProgressModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
