import 'dart:io';
import '../enums/file_type.dart';
import '../enums/download_state.dart';
import '../enums/viewer_type.dart';

/// Model representing a file that can be either local or remote
class FileModel {
  /// Unique identifier for the file
  final String id;
  
  /// File name with extension
  final String fileName;
  
  /// File size in bytes (null if unknown)
  final int? fileSizeBytes;
  
  /// File extension (without dot)
  final String fileExtension;
  
  /// File type based on extension
  final FileType fileType;
  
  /// URL for remote files (null for local files)
  final String? url;
  
  /// Local file path (null for remote files that haven't been downloaded)
  final String? localPath;
  
  /// Whether this is a local file or remote file
  final bool isLocal;
  
  /// Current download state (only relevant for remote files)
  final DownloadState downloadState;
  
  /// Download progress (0.0 to 1.0, only relevant when downloading)
  final double downloadProgress;
  
  /// Error message if download failed
  final String? downloadError;
  
  /// When the file was created/uploaded
  final DateTime? createdAt;
  
  /// When the file was last modified
  final DateTime? modifiedAt;
  
  /// MIME type of the file
  final String? mimeType;

  const FileModel({
    required this.id,
    required this.fileName,
    this.fileSizeBytes,
    required this.fileExtension,
    required this.fileType,
    this.url,
    this.localPath,
    required this.isLocal,
    this.downloadState = DownloadState.notDownloaded,
    this.downloadProgress = 0.0,
    this.downloadError,
    this.createdAt,
    this.modifiedAt,
    this.mimeType,
  });

  /// Create a FileModel from a local file
  factory FileModel.fromLocalFile(File file) {
    final fileName = file.path.split('/').last;
    final extension = fileName.split('.').last.toLowerCase();
    final fileType = FileTypeDetector.fromExtension(extension);
    
    int? fileSize;
    DateTime? modifiedAt;
    
    try {
      fileSize = file.lengthSync();
      modifiedAt = file.lastModifiedSync();
    } catch (e) {
      // File might not exist or be accessible
    }
    
    return FileModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      fileName: fileName,
      fileSizeBytes: fileSize,
      fileExtension: extension,
      fileType: fileType,
      localPath: file.path,
      isLocal: true,
      downloadState: DownloadState.downloaded,
      downloadProgress: 1.0,
      modifiedAt: modifiedAt,
    );
  }

  /// Create a FileModel from a remote URL
  factory FileModel.fromUrl(String url, {String? customFileName}) {
    final fileName = customFileName ?? url.split('/').last;
    final extension = fileName.split('.').last.toLowerCase();
    final fileType = FileTypeDetector.fromExtension(extension);
    
    return FileModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      fileName: fileName,
      fileExtension: extension,
      fileType: fileType,
      url: url,
      isLocal: false,
      downloadState: DownloadState.notDownloaded,
      downloadProgress: 0.0,
      createdAt: DateTime.now(),
    );
  }

  /// Get the appropriate viewer type for this file
  ViewerType get viewerType {
    switch (fileType) {
      case FileType.image:
        return ViewerType.image;
      case FileType.pdf:
        return ViewerType.pdf;
      case FileType.video:
      case FileType.document:
      case FileType.audio:
      case FileType.archive:
      case FileType.other:
        return ViewerType.placeholder;
    }
  }

  /// Get the file size as a human-readable string
  String get fileSizeString {
    if (fileSizeBytes == null) return 'Unknown size';
    return _formatFileSize(fileSizeBytes!);
  }

  /// Whether the file is available for viewing (either local or downloaded)
  bool get isAvailableForViewing {
    return isLocal || downloadState == DownloadState.downloaded;
  }

  /// Get the path to use for viewing (local path or downloaded path)
  String? get viewingPath {
    return localPath;
  }

  /// Whether the file can be downloaded
  bool get canDownload {
    return !isLocal && url != null;
  }

  /// Whether the file is currently being downloaded
  bool get isDownloading {
    return downloadState == DownloadState.downloading;
  }

  /// Create a copy of this FileModel with updated properties
  FileModel copyWith({
    String? id,
    String? fileName,
    int? fileSizeBytes,
    String? fileExtension,
    FileType? fileType,
    String? url,
    String? localPath,
    bool? isLocal,
    DownloadState? downloadState,
    double? downloadProgress,
    String? downloadError,
    DateTime? createdAt,
    DateTime? modifiedAt,
    String? mimeType,
  }) {
    return FileModel(
      id: id ?? this.id,
      fileName: fileName ?? this.fileName,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      fileExtension: fileExtension ?? this.fileExtension,
      fileType: fileType ?? this.fileType,
      url: url ?? this.url,
      localPath: localPath ?? this.localPath,
      isLocal: isLocal ?? this.isLocal,
      downloadState: downloadState ?? this.downloadState,
      downloadProgress: downloadProgress ?? this.downloadProgress,
      downloadError: downloadError ?? this.downloadError,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      mimeType: mimeType ?? this.mimeType,
    );
  }

  /// Format file size in bytes to human-readable string
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  @override
  String toString() {
    return 'FileModel(id: $id, fileName: $fileName, fileType: $fileType, '
        'isLocal: $isLocal, downloadState: $downloadState)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FileModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
