import 'dart:io';

/// Model representing an uploaded file for homework submission
class UploadedFileModel {
  final String id;
  final File file;
  final String fileName;
  final String fileSize;
  final String fileExtension;
  final DateTime uploadedAt;
  final FileType fileType;

  const UploadedFileModel({
    required this.id,
    required this.file,
    required this.fileName,
    required this.fileSize,
    required this.fileExtension,
    required this.uploadedAt,
    required this.fileType,
  });

  /// Create an UploadedFileModel from a File
  factory UploadedFileModel.fromFile(File file) {
    final fileName = file.path.split('/').last;
    final extension = fileName.split('.').last.toLowerCase();
    final bytes = file.lengthSync();
    
    return UploadedFileModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      file: file,
      fileName: fileName,
      fileSize: _formatFileSize(bytes),
      fileExtension: extension,
      uploadedAt: DateTime.now(),
      fileType: _getFileType(extension),
    );
  }

  /// Format file size in human-readable format
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Determine file type based on extension
  static FileType _getFileType(String extension) {
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
      return FileType.image;
    } else if (['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', '3gp'].contains(extension)) {
      return FileType.video;
    } else if (['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].contains(extension)) {
      return FileType.document;
    } else {
      return FileType.other;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UploadedFileModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UploadedFileModel(id: $id, fileName: $fileName, fileSize: $fileSize)';
  }
}

/// Enum representing different file types
enum FileType {
  image,
  video,
  document,
  other,
}

/// Extension to provide additional functionality for FileType
extension FileTypeExtension on FileType {
  /// Returns a human-readable label for the file type
  String get label {
    switch (this) {
      case FileType.image:
        return 'Image';
      case FileType.video:
        return 'Video';
      case FileType.document:
        return 'Document';
      case FileType.other:
        return 'File';
    }
  }

  /// Returns the primary color for the file type
  String get colorHex {
    switch (this) {
      case FileType.image:
        return '#4CAF50'; // Green
      case FileType.video:
        return '#FF5722'; // Deep Orange
      case FileType.document:
        return '#2196F3'; // Blue
      case FileType.other:
        return '#9E9E9E'; // Grey
    }
  }
}
