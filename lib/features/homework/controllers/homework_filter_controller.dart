import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../core/enums/homework/assignment_type.dart';
import '../models/homework_model.dart';
import 'homework_controller.dart';

final Logger _logger = Logger();

/// Enum for homework filter types
enum HomeworkFilterType { all, classAssignments, individual, group }

/// Extension for HomeworkFilterType
extension HomeworkFilterTypeExtension on HomeworkFilterType {
  String get label {
    switch (this) {
      case HomeworkFilterType.all:
        return 'All';
      case HomeworkFilterType.classAssignments:
        return 'Class Assignments';
      case HomeworkFilterType.individual:
        return 'Individual';
      case HomeworkFilterType.group:
        return 'Group';
    }
  }

  String get shortLabel {
    switch (this) {
      case HomeworkFilterType.all:
        return 'All';
      case HomeworkFilterType.classAssignments:
        return 'Class';
      case HomeworkFilterType.individual:
        return 'Individual';
      case HomeworkFilterType.group:
        return 'Group';
    }
  }
}

/// State notifier for homework filter type
class HomeworkFilterNotifier extends StateNotifier<HomeworkFilterType> {
  HomeworkFilterNotifier() : super(HomeworkFilterType.all);

  void setFilter(HomeworkFilterType filterType) {
    _logger.i('Setting homework filter to: ${filterType.label}');
    state = filterType;
  }

  void reset() {
    _logger.i('Resetting homework filter to All');
    state = HomeworkFilterType.all;
  }
}

/// Provider for homework filter state
final homeworkFilterProvider =
    StateNotifierProvider<HomeworkFilterNotifier, HomeworkFilterType>((ref) {
      return HomeworkFilterNotifier();
    });

/// State notifier for selected class filter
class ClassFilterNotifier extends StateNotifier<String?> {
  ClassFilterNotifier() : super(null);

  void setClass(String? classId) {
    _logger.i('Setting class filter to: $classId');
    state = classId;
  }

  void clearClass() {
    _logger.i('Clearing class filter');
    state = null;
  }
}

/// Provider for class filter state
final classFilterProvider = StateNotifierProvider<ClassFilterNotifier, String?>(
  (ref) {
    return ClassFilterNotifier();
  },
);

/// Provider that combines filter state and returns filtered homework
final filteredHomeworkProvider = FutureProvider<List<HomeworkModel>>((
  ref,
) async {
  final filterType = ref.watch(homeworkFilterProvider);
  final selectedClassId = ref.watch(classFilterProvider);

  try {
    _logger.i(
      'Applying homework filter: ${filterType.label}, class: $selectedClassId',
    );

    // Always get all user homework first to provide unified loading experience
    final allUserHomework = await ref.read(userHomeworkProvider.future);

    List<HomeworkModel> homeworkList;

    // Filter based on assignment type
    switch (filterType) {
      case HomeworkFilterType.all:
        homeworkList = allUserHomework;
        break;
      case HomeworkFilterType.classAssignments:
        homeworkList = allUserHomework
            .where((hw) => hw.assignmentType == AssignmentType.classAssignment)
            .toList();
        break;
      case HomeworkFilterType.individual:
        homeworkList = allUserHomework
            .where((hw) => hw.assignmentType == AssignmentType.individual)
            .toList();
        break;
      case HomeworkFilterType.group:
        homeworkList = allUserHomework
            .where((hw) => hw.assignmentType == AssignmentType.group)
            .toList();
        break;
    }

    // Apply class filter if selected
    if (selectedClassId != null &&
        filterType == HomeworkFilterType.classAssignments) {
      homeworkList = homeworkList
          .where((homework) => homework.classId == selectedClassId)
          .toList();
    }

    _logger.i('Filtered homework result: ${homeworkList.length} items');
    return homeworkList;
  } catch (e) {
    _logger.e('Error applying homework filters: $e');
    rethrow;
  }
});

/// Provider for filtered homework by date
final filteredHomeworkByDateProvider =
    FutureProvider.family<List<HomeworkModel>, DateTime>((ref, date) async {
      final filterType = ref.watch(homeworkFilterProvider);
      final selectedClassId = ref.watch(classFilterProvider);

      try {
        _logger.i(
          'Applying homework filter for date ${date.toIso8601String()}: ${filterType.label}, class: $selectedClassId',
        );

        // Get homework for the specific date first (unified loading)
        final allHomeworkForDate = await ref.read(
          homeworkListProvider(date).future,
        );

        // Filter based on assignment type
        List<HomeworkModel> filteredHomework;

        switch (filterType) {
          case HomeworkFilterType.all:
            filteredHomework = allHomeworkForDate;
            break;
          case HomeworkFilterType.classAssignments:
            filteredHomework = allHomeworkForDate
                .where(
                  (hw) => hw.assignmentType == AssignmentType.classAssignment,
                )
                .toList();
            break;
          case HomeworkFilterType.individual:
            filteredHomework = allHomeworkForDate
                .where((hw) => hw.assignmentType == AssignmentType.individual)
                .toList();
            break;
          case HomeworkFilterType.group:
            filteredHomework = allHomeworkForDate
                .where((hw) => hw.assignmentType == AssignmentType.group)
                .toList();
            break;
        }

        // Apply class filter if selected
        if (selectedClassId != null &&
            filterType == HomeworkFilterType.classAssignments) {
          filteredHomework = filteredHomework
              .where((homework) => homework.classId == selectedClassId)
              .toList();
        }

        _logger.i(
          'Filtered homework for date result: ${filteredHomework.length} items',
        );
        return filteredHomework;
      } catch (e) {
        _logger.e('Error applying homework filters for date: $e');
        rethrow;
      }
    });

/// Provider to get available filter options with counts
final filterOptionsProvider = FutureProvider<Map<HomeworkFilterType, int>>((
  ref,
) async {
  try {
    // Use unified homework loading for consistent experience
    final allHomework = await ref.read(userHomeworkProvider.future);

    final counts = <HomeworkFilterType, int>{
      HomeworkFilterType.all: allHomework.length,
      HomeworkFilterType.classAssignments: allHomework
          .where((hw) => hw.assignmentType == AssignmentType.classAssignment)
          .length,
      HomeworkFilterType.individual: allHomework
          .where((hw) => hw.assignmentType == AssignmentType.individual)
          .length,
      HomeworkFilterType.group: allHomework
          .where((hw) => hw.assignmentType == AssignmentType.group)
          .length,
    };

    _logger.i('Filter options with counts: $counts');
    return counts;
  } catch (e) {
    _logger.e('Error getting filter options: $e');
    rethrow;
  }
});
