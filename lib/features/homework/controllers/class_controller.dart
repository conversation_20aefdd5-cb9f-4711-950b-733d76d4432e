import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../classroom/models/class_model.dart';
import '../repositories/class_repository.dart';

final Logger _logger = Logger();

/// Provider for ClassRepository instance
final classRepositoryProvider = Provider<ClassRepository>((ref) {
  return ClassRepository();
});

/// Provider to fetch all active classes
final allClassesProvider = FutureProvider<List<ClassModel>>((ref) async {
  final repository = ref.read(classRepositoryProvider);

  try {
    _logger.i('Fetching all classes');
    final classList = await repository.getAllClasses();
    _logger.i('Successfully fetched ${classList.length} classes');
    return classList;
  } catch (e) {
    _logger.e('Error fetching all classes: $e');
    rethrow;
  }
});

/// Provider to fetch a specific class by ID
final classDetailProvider = FutureProvider.family<ClassModel?, String>((
  ref,
  classId,
) async {
  final repository = ref.read(classRepositoryProvider);

  try {
    _logger.i('Fetching class detail for ID: $classId');
    final classModel = await repository.getClassById(classId);
    if (classModel != null) {
      _logger.i('Successfully fetched class detail: ${classModel.name}');
    } else {
      _logger.w('Class not found with ID: $classId');
    }
    return classModel;
  } catch (e) {
    _logger.e('Error fetching class detail: $e');
    rethrow;
  }
});

/// Provider to fetch classes for a specific student
final studentClassesProvider = FutureProvider.family<List<ClassModel>, String>((
  ref,
  studentId,
) async {
  final repository = ref.read(classRepositoryProvider);

  try {
    _logger.i('Fetching classes for student: $studentId');
    final classList = await repository.getClassesForStudent(studentId);
    _logger.i(
      'Successfully fetched ${classList.length} classes for student $studentId',
    );
    return classList;
  } catch (e) {
    _logger.e('Error fetching classes for student: $e');
    rethrow;
  }
});

/// Provider to fetch classes for a specific teacher
final teacherClassesProvider = FutureProvider.family<List<ClassModel>, String>((
  ref,
  teacherId,
) async {
  final repository = ref.read(classRepositoryProvider);

  try {
    _logger.i('Fetching classes for teacher: $teacherId');
    final classList = await repository.getClassesForTeacher(teacherId);
    _logger.i(
      'Successfully fetched ${classList.length} classes for teacher $teacherId',
    );
    return classList;
  } catch (e) {
    _logger.e('Error fetching classes for teacher: $e');
    rethrow;
  }
});

/// Provider to create a new class
final createClassProvider = FutureProvider.family<void, ClassModel>((
  ref,
  classModel,
) async {
  final repository = ref.read(classRepositoryProvider);

  try {
    _logger.i('Creating new class: ${classModel.name}');
    await repository.createClass(classModel);
    _logger.i('Successfully created class');

    // Invalidate related providers to refresh UI
    ref.invalidate(allClassesProvider);
    ref.invalidate(studentClassesProvider);
    ref.invalidate(teacherClassesProvider);
  } catch (e) {
    _logger.e('Error creating class: $e');
    rethrow;
  }
});

/// Provider to update an existing class
final updateClassProvider = FutureProvider.family<void, ClassModel>((
  ref,
  classModel,
) async {
  final repository = ref.read(classRepositoryProvider);

  try {
    _logger.i('Updating class: ${classModel.name}');
    await repository.updateClass(classModel);
    _logger.i('Successfully updated class');

    // Invalidate related providers to refresh UI
    ref.invalidate(allClassesProvider);
    ref.invalidate(classDetailProvider(classModel.id));
    ref.invalidate(studentClassesProvider);
    ref.invalidate(teacherClassesProvider);
  } catch (e) {
    _logger.e('Error updating class: $e');
    rethrow;
  }
});

/// Provider to delete a class
final deleteClassProvider = FutureProvider.family<void, String>((
  ref,
  classId,
) async {
  final repository = ref.read(classRepositoryProvider);

  try {
    _logger.i('Deleting class: $classId');
    await repository.deleteClass(classId);
    _logger.i('Successfully deleted class');

    // Invalidate related providers to refresh UI
    ref.invalidate(allClassesProvider);
    ref.invalidate(classDetailProvider(classId));
    ref.invalidate(studentClassesProvider);
    ref.invalidate(teacherClassesProvider);
  } catch (e) {
    _logger.e('Error deleting class: $e');
    rethrow;
  }
});

/// Provider to check if a student is enrolled in a specific class
final studentEnrollmentProvider =
    FutureProvider.family<bool, ({String studentId, String classId})>((
      ref,
      params,
    ) async {
      final repository = ref.read(classRepositoryProvider);

      try {
        _logger.i(
          'Checking enrollment for student ${params.studentId} in class ${params.classId}',
        );
        final isEnrolled = await repository.isStudentInClass(
          params.studentId,
          params.classId,
        );
        _logger.i('Enrollment status: $isEnrolled');
        return isEnrolled;
      } catch (e) {
        _logger.e('Error checking student enrollment: $e');
        rethrow;
      }
    });
