import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../controllers/unified_homework_controller.dart';
import '../models/homework_filter_config.dart';
import '../models/homework_model.dart';
import '../services/homework_filter_service.dart';

/// Logger for filter state controller
final _logger = Logger();

/// Enhanced filter state that includes additional context
class HomeworkFilterState {
  final UnifiedHomeworkFilterType filterType;
  final String? selectedClassId;
  final DateTime? selectedDate;
  final Map<UnifiedHomeworkFilterType, int> filterCounts;
  final bool isLoading;
  final String? error;

  const HomeworkFilterState({
    required this.filterType,
    this.selectedClassId,
    this.selectedDate,
    this.filterCounts = const {},
    this.isLoading = false,
    this.error,
  });

  HomeworkFilterState copyWith({
    UnifiedHomeworkFilterType? filterType,
    String? selectedClassId,
    DateTime? selectedDate,
    Map<UnifiedHomeworkFilterType, int>? filterCounts,
    bool? isLoading,
    String? error,
  }) {
    return HomeworkFilterState(
      filterType: filterType ?? this.filterType,
      selectedClassId: selectedClassId ?? this.selectedClassId,
      selectedDate: selectedDate ?? this.selectedDate,
      filterCounts: filterCounts ?? this.filterCounts,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HomeworkFilterState &&
        other.filterType == filterType &&
        other.selectedClassId == selectedClassId &&
        other.selectedDate == selectedDate &&
        other.isLoading == isLoading &&
        other.error == error;
  }

  @override
  int get hashCode {
    return Object.hash(
      filterType,
      selectedClassId,
      selectedDate,
      isLoading,
      error,
    );
  }

  @override
  String toString() {
    return 'HomeworkFilterState(filterType: $filterType, selectedClassId: $selectedClassId, selectedDate: $selectedDate, isLoading: $isLoading, error: $error)';
  }
}

/// Enhanced filter state notifier with service integration
class HomeworkFilterStateNotifier extends StateNotifier<HomeworkFilterState> {
  final HomeworkFilterService _filterService;

  HomeworkFilterStateNotifier(this._filterService)
    : super(
        HomeworkFilterState(filterType: _filterService.getDefaultFilter().type),
      ) {
    _logger.i('Initialized HomeworkFilterStateNotifier');
  }

  /// Set the filter type
  void setFilterType(UnifiedHomeworkFilterType filterType) {
    if (!_filterService.canTransitionTo(state.filterType, filterType)) {
      _logger.w(
        'Filter transition not allowed: ${state.filterType} -> $filterType',
      );
      return;
    }

    _logger.i('Setting filter type to: $filterType');

    state = state.copyWith(
      filterType: filterType,
      // Clear class selection if not relevant for new filter
      selectedClassId: filterType == UnifiedHomeworkFilterType.classAssignments
          ? state.selectedClassId
          : null,
      error: null,
    );
  }

  /// Set the selected class ID
  void setSelectedClassId(String? classId) {
    _logger.i('Setting selected class ID to: $classId');
    state = state.copyWith(selectedClassId: classId, error: null);
  }

  /// Set the selected date
  void setSelectedDate(DateTime? date) {
    _logger.i('Setting selected date to: ${date?.toIso8601String()}');
    state = state.copyWith(selectedDate: date, error: null);
  }

  /// Update filter counts
  void updateFilterCounts(Map<UnifiedHomeworkFilterType, int> counts) {
    _logger.d('Updating filter counts: $counts');
    state = state.copyWith(filterCounts: counts);
  }

  /// Set loading state
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// Set error state
  void setError(String? error) {
    _logger.e('Filter error: $error');
    state = state.copyWith(error: error, isLoading: false);
  }

  /// Reset to default state
  void reset() {
    _logger.i('Resetting filter state to default');
    state = HomeworkFilterState(
      filterType: _filterService.getDefaultFilter().type,
    );
  }

  /// Get recommended filters based on current state
  List<UnifiedHomeworkFilterType> getRecommendedFilters() {
    return _filterService.getRecommendedFilters(
      state.filterType,
      state.filterCounts,
    );
  }

  /// Check if class selection is required for current filter
  bool get requiresClassSelection {
    return state.filterType == UnifiedHomeworkFilterType.classAssignments;
  }

  /// Get current filter configuration
  HomeworkFilterConfig? get currentFilterConfig {
    return _filterService.getFilter(state.filterType);
  }
}

/// Provider for the filter service
final homeworkFilterServiceProvider = Provider<HomeworkFilterService>((ref) {
  final service = HomeworkFilterService();
  service.initialize();
  return service;
});

/// Provider for the enhanced filter state
final homeworkFilterStateProvider =
    StateNotifierProvider<HomeworkFilterStateNotifier, HomeworkFilterState>((
      ref,
    ) {
      final filterService = ref.read(homeworkFilterServiceProvider);
      return HomeworkFilterStateNotifier(filterService);
    });

/// Provider for filtered homework using the enhanced state management
final enhancedFilteredHomeworkProvider = FutureProvider<List<HomeworkModel>>((
  ref,
) async {
  final filterState = ref.watch(homeworkFilterStateProvider);
  final filterService = ref.read(homeworkFilterServiceProvider);

  try {
    _logger.i('Applying enhanced filters: ${filterState.filterType}');

    // Get all user homework from the unified provider
    final allUserHomework = await ref.read(unifiedHomeworkProvider.future);

    // Apply filters using the service
    final filteredHomework = filterService.applyFilters(
      allUserHomework,
      filterState.filterType,
      filterState.selectedClassId,
    );

    // Update filter counts
    final counts = filterService.calculateFilterCounts(
      allUserHomework,
      cacheKey: 'all_homework',
    );

    // Update the state with new counts (but don't trigger rebuild)
    Future.microtask(() {
      ref.read(homeworkFilterStateProvider.notifier).updateFilterCounts(counts);
    });

    _logger.i('Enhanced filter result: ${filteredHomework.length} items');
    return filteredHomework;
  } catch (e) {
    _logger.e('Error in enhanced filtered homework: $e');

    // Update error state
    Future.microtask(() {
      ref.read(homeworkFilterStateProvider.notifier).setError(e.toString());
    });

    rethrow;
  }
});

/// Provider for filtered homework by date using enhanced state management
final enhancedFilteredHomeworkByDateProvider =
    FutureProvider.family<List<HomeworkModel>, DateTime>((ref, date) async {
      final filterState = ref.watch(homeworkFilterStateProvider);
      final filterService = ref.read(homeworkFilterServiceProvider);

      try {
        _logger.i(
          'Applying enhanced filters by date: ${filterState.filterType}, date: ${date.toIso8601String()}',
        );

        // Get homework for the specific date from the unified provider
        final allHomeworkForDate = await ref.read(
          unifiedHomeworkByDateProvider(date).future,
        );

        // Apply filters using the service
        final filteredHomework = filterService.applyFilters(
          allHomeworkForDate,
          filterState.filterType,
          filterState.selectedClassId,
        );

        _logger.i(
          'Enhanced filter by date result: ${filteredHomework.length} items',
        );
        return filteredHomework;
      } catch (e) {
        _logger.e('Error in enhanced filtered homework by date: $e');

        // Update error state
        Future.microtask(() {
          ref.read(homeworkFilterStateProvider.notifier).setError(e.toString());
        });

        rethrow;
      }
    });

/// Provider for filter options with enhanced state management
final enhancedFilterOptionsProvider =
    FutureProvider<Map<UnifiedHomeworkFilterType, int>>((ref) async {
      final filterService = ref.read(homeworkFilterServiceProvider);

      try {
        // Get all user homework from the unified provider
        final allHomework = await ref.read(unifiedHomeworkProvider.future);

        // Calculate filter counts using the service
        final counts = filterService.calculateFilterCounts(
          allHomework,
          cacheKey: 'filter_options',
        );

        _logger.i('Enhanced filter options calculated: $counts');
        return counts;
      } catch (e) {
        _logger.e('Error calculating enhanced filter options: $e');
        rethrow;
      }
    });

/// Utility provider for filter analytics
final filterAnalyticsProvider = Provider<Map<String, dynamic>>((ref) {
  final filterState = ref.watch(homeworkFilterStateProvider);
  final filterService = ref.read(homeworkFilterServiceProvider);

  return {
    'current_filter': filterState.filterType.toString(),
    'selected_class': filterState.selectedClassId,
    'selected_date': filterState.selectedDate?.toIso8601String(),
    'filter_counts': filterState.filterCounts,
    'service_stats': filterService.getFilterStatistics(),
  };
});
