import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../classroom/mock/mock_classes.dart';
import '../../profile/mock/upload_mock_profiles.dart';
import 'mock_homeworks.dart';

/// Upload all mock data (classes, homework, submissions, and profiles) to Firestore
Future<void> uploadAllMockData() async {
  try {
    debugPrint('=== Starting comprehensive mock data upload ===');

    // Upload in order: classes first, then profiles, then homework, then submissions
    await uploadMockClasses();

    // Upload profiles
    final profileUploader = UploadMockProfiles();
    await profileUploader.uploadAllProfiles();

    await uploadMockHomework();
    await uploadMockSubmissions();

    debugPrint('=== All mock data upload completed successfully! ===');
  } catch (e) {
    debugPrint('Error during comprehensive mock data upload: $e');
    rethrow;
  }
}

/// Upload mock classroom data to Firestore
Future<void> uploadMockClasses() async {
  final firestore = FirebaseFirestore.instance;

  try {
    debugPrint('📚 Starting classroom data upload...');
    debugPrint('Total classes to upload: ${mockClassesList.length}');

    int successCount = 0;
    int errorCount = 0;

    for (final classModel in mockClassesList) {
      try {
        final classDoc = firestore.collection('classes').doc(classModel.id);
        await classDoc.set(classModel.toJson());
        successCount++;
        debugPrint(
          '✅ Uploaded class: ${classModel.name} (${classModel.subject})',
        );
      } catch (e) {
        errorCount++;
        debugPrint('❌ Failed to upload class ${classModel.id}: $e');
      }
    }

    debugPrint(
      '📚 Classroom upload summary: $successCount successful, $errorCount failed',
    );

    if (errorCount > 0) {
      throw Exception('Failed to upload $errorCount classes');
    }
  } catch (e) {
    debugPrint('Error uploading classroom data: $e');
    rethrow;
  }
}

/// Upload mock homework data to Firestore
Future<void> uploadMockHomework() async {
  final firestore = FirebaseFirestore.instance;

  try {
    debugPrint('📝 Starting homework data upload...');
    debugPrint('Total homework items to upload: ${mockHomeworkList.length}');

    int successCount = 0;
    int errorCount = 0;
    final statusCounts = <String, int>{};

    for (final homework in mockHomeworkList) {
      try {
        final homeworkDoc = firestore.collection('homeworks').doc(homework.id);
        await homeworkDoc.set(homework.toJson());
        successCount++;

        // Track status distribution
        final status = homework.status.name;
        statusCounts[status] = (statusCounts[status] ?? 0) + 1;

        debugPrint(
          '✅ Uploaded homework: ${homework.title} (${homework.status.name})',
        );
      } catch (e) {
        errorCount++;
        debugPrint('❌ Failed to upload homework ${homework.id}: $e');
      }
    }

    debugPrint(
      '📝 Homework upload summary: $successCount successful, $errorCount failed',
    );
    debugPrint('📊 Status distribution: $statusCounts');

    if (errorCount > 0) {
      throw Exception('Failed to upload $errorCount homework items');
    }
  } catch (e) {
    debugPrint('Error uploading homework data: $e');
    rethrow;
  }
}

/// Upload mock homework submission data to Firestore
Future<void> uploadMockSubmissions() async {
  final firestore = FirebaseFirestore.instance;

  try {
    debugPrint('📤 Starting submission data upload...');
    debugPrint(
      'Total submissions to upload: ${mockHomeworkSubmissions.length}',
    );

    int successCount = 0;
    int errorCount = 0;
    int reviewedCount = 0;

    for (final submission in mockHomeworkSubmissions) {
      try {
        final submissionDoc = firestore
            .collection('homework_submissions')
            .doc(submission.id);
        await submissionDoc.set(submission.toJson());
        successCount++;

        if (submission.teacherRemark != null) {
          reviewedCount++;
        }

        debugPrint('✅ Uploaded submission: ${submission.id}');
      } catch (e) {
        errorCount++;
        debugPrint('❌ Failed to upload submission ${submission.id}: $e');
      }
    }

    debugPrint(
      '📤 Submission upload summary: $successCount successful, $errorCount failed',
    );
    debugPrint('📊 Reviewed submissions: $reviewedCount out of $successCount');

    if (errorCount > 0) {
      throw Exception('Failed to upload $errorCount submissions');
    }
  } catch (e) {
    debugPrint('Error uploading submission data: $e');
    rethrow;
  }
}

/// Clear all mock data from Firestore (useful for testing)
Future<void> clearAllMockData() async {
  final firestore = FirebaseFirestore.instance;

  try {
    debugPrint('🗑️ Starting mock data cleanup...');

    // Clear submissions first (to avoid foreign key issues)
    await _clearCollection(firestore, 'homework_submissions');
    await _clearCollection(firestore, 'homeworks');
    await _clearCollection(firestore, 'classes');

    debugPrint('🗑️ Mock data cleanup completed successfully!');
  } catch (e) {
    debugPrint('Error during mock data cleanup: $e');
    rethrow;
  }
}

/// Helper function to clear a Firestore collection
Future<void> _clearCollection(
  FirebaseFirestore firestore,
  String collectionName,
) async {
  try {
    debugPrint('Clearing collection: $collectionName');

    final querySnapshot = await firestore.collection(collectionName).get();
    final batch = firestore.batch();

    for (final doc in querySnapshot.docs) {
      batch.delete(doc.reference);
    }

    await batch.commit();
    debugPrint(
      '✅ Cleared ${querySnapshot.docs.length} documents from $collectionName',
    );
  } catch (e) {
    debugPrint('❌ Error clearing collection $collectionName: $e');
    rethrow;
  }
}

/// Legacy function for backward compatibility
@Deprecated('Use uploadAllMockData() instead')
Future<void> uploadMockHomeworkData() async {
  await uploadAllMockData();
}
