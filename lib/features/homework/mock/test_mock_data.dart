import 'package:flutter/foundation.dart';
import '../../../core/enums/homework/assignment_type.dart';
import '../../../core/enums/homework/homework_status.dart';
import '../../../core/enums/homework/submission_type.dart';
import '../../classroom/mock/mock_classes.dart';
import 'mock_homeworks.dart';

/// Test function to verify mock data generation coverage
void testMockDataCoverage() {
  debugPrint('=== Testing Mock Data Coverage ===');

  _testClassroomData();
  _testHomeworkData();
  _testSubmissionData();

  debugPrint('=== Mock Data Coverage Test Completed ===');
}

/// Test classroom data coverage
void _testClassroomData() {
  debugPrint('\n📚 Testing Classroom Data:');
  debugPrint('Total classes: ${mockClassesList.length}');

  final subjectCounts = <String, int>{};
  int totalStudents = 0;

  for (final classModel in mockClassesList) {
    final subject = classModel.subject ?? 'General';
    subjectCounts[subject] = (subjectCounts[subject] ?? 0) + 1;
    totalStudents += classModel.studentIds.length;
  }

  debugPrint('Subject distribution: $subjectCounts');
  debugPrint(
    'Average students per class: ${(totalStudents / mockClassesList.length).toStringAsFixed(1)}',
  );

  // Verify we have diverse subjects
  if (subjectCounts.length >= 3) {
    debugPrint('✅ Good subject diversity');
  } else {
    debugPrint('⚠️ Limited subject diversity');
  }
}

/// Test homework data coverage
void _testHomeworkData() {
  debugPrint('\n📝 Testing Homework Data:');
  debugPrint('Total homework items: ${mockHomeworkList.length}');

  // Test status coverage
  final statusCounts = <HomeworkStatus, int>{};
  final submissionTypeCounts = <SubmissionType, int>{};
  final assignmentTypeCounts = <AssignmentType, int>{};
  final subjectCounts = <String, int>{};

  int onlineSubmissionCount = 0;
  int offlineSubmissionCount = 0;
  int withDueDateCount = 0;
  int withDescriptionCount = 0;
  int withResourcesCount = 0;
  int withTeacherNoteCount = 0;

  for (final homework in mockHomeworkList) {
    // Count statuses
    statusCounts[homework.status] = (statusCounts[homework.status] ?? 0) + 1;

    // Count submission types
    submissionTypeCounts[homework.submissionType] =
        (submissionTypeCounts[homework.submissionType] ?? 0) + 1;

    // Count assignment types
    assignmentTypeCounts[homework.assignmentType] =
        (assignmentTypeCounts[homework.assignmentType] ?? 0) + 1;

    // Count subjects
    subjectCounts[homework.subject] =
        (subjectCounts[homework.subject] ?? 0) + 1;

    // Count various attributes
    if (homework.submissionType == SubmissionType.online) {
      onlineSubmissionCount++;
    }
    if (homework.submissionType == SubmissionType.offline) {
      offlineSubmissionCount++;
    }
    if (homework.dueAt != null) {
      withDueDateCount++;
    }
    if (homework.description != null) {
      withDescriptionCount++;
    }
    if (homework.resourceUrls.isNotEmpty) {
      withResourcesCount++;
    }
    if (homework.teacherNote != null) {
      withTeacherNoteCount++;
    }
  }

  debugPrint('Status distribution: $statusCounts');
  debugPrint('Submission type distribution: $submissionTypeCounts');
  debugPrint('Assignment type distribution: $assignmentTypeCounts');
  debugPrint('Subject distribution: $subjectCounts');

  debugPrint('\nAttribute coverage:');
  debugPrint('- Online submissions: $onlineSubmissionCount');
  debugPrint('- Offline submissions: $offlineSubmissionCount');
  debugPrint('- With due dates: $withDueDateCount');
  debugPrint('- With descriptions: $withDescriptionCount');
  debugPrint('- With resources: $withResourcesCount');
  debugPrint('- With teacher notes: $withTeacherNoteCount');

  // Verify minimum coverage for each status
  bool hasMinimumCoverage = true;
  for (final status in HomeworkStatus.values) {
    final count = statusCounts[status] ?? 0;
    if (count < 3) {
      debugPrint(
        '⚠️ Status ${status.name} has only $count items (minimum 3 required)',
      );
      hasMinimumCoverage = false;
    }
  }

  if (hasMinimumCoverage) {
    debugPrint('✅ All homework statuses have minimum 3 test cases');
  }

  // Verify assignment type coverage
  if (assignmentTypeCounts.length >= 3) {
    debugPrint('✅ Good assignment type diversity');
  } else {
    debugPrint('⚠️ Limited assignment type diversity');
  }

  // Verify submission type coverage
  if (submissionTypeCounts.length >= 2) {
    debugPrint('✅ Both online and offline submission types covered');
  } else {
    debugPrint('⚠️ Missing submission type coverage');
  }
}

/// Test submission data coverage
void _testSubmissionData() {
  debugPrint('\n📤 Testing Submission Data:');
  debugPrint('Total submissions: ${mockHomeworkSubmissions.length}');

  int withTeacherRemarkCount = 0;
  int reviewedCount = 0;
  int withStudentNoteCount = 0;
  final fileTypeCounts = <String, int>{};

  for (final submission in mockHomeworkSubmissions) {
    if (submission.teacherRemark != null) {
      withTeacherRemarkCount++;
    }
    if (submission.reviewedAt != null) {
      reviewedCount++;
    }
    if (submission.studentNote != null) {
      withStudentNoteCount++;
    }

    // Count file types
    for (final fileUrl in submission.fileUrls) {
      final extension = fileUrl.split('.').last.toLowerCase();
      fileTypeCounts[extension] = (fileTypeCounts[extension] ?? 0) + 1;
    }
  }

  debugPrint('Submission attributes:');
  debugPrint('- With teacher remarks: $withTeacherRemarkCount');
  debugPrint('- Reviewed: $reviewedCount');
  debugPrint('- With student notes: $withStudentNoteCount');
  debugPrint('- File type distribution: $fileTypeCounts');

  // Verify submission coverage
  if (mockHomeworkSubmissions.isNotEmpty) {
    debugPrint('✅ Submission data generated');
  } else {
    debugPrint('⚠️ No submission data found');
  }

  if (withTeacherRemarkCount > 0) {
    debugPrint('✅ Teacher remarks included');
  } else {
    debugPrint('⚠️ No teacher remarks found');
  }

  if (fileTypeCounts.length >= 3) {
    debugPrint('✅ Good file type diversity');
  } else {
    debugPrint('⚠️ Limited file type diversity');
  }
}

/// Test data relationships and consistency
void testDataConsistency() {
  debugPrint('\n🔗 Testing Data Consistency:');

  // Test homework-submission relationships
  final homeworkWithSubmissions = mockHomeworkList
      .where(
        (hw) =>
            hw.status == HomeworkStatus.submitted ||
            hw.status == HomeworkStatus.accepted ||
            hw.status == HomeworkStatus.rejected,
      )
      .toList();

  final submissionHomeworkIds = mockHomeworkSubmissions
      .map((sub) => sub.homeworkId)
      .toSet();

  debugPrint(
    'Homework requiring submissions: ${homeworkWithSubmissions.length}',
  );
  debugPrint('Actual submissions: ${mockHomeworkSubmissions.length}');
  debugPrint(
    'Unique homework IDs in submissions: ${submissionHomeworkIds.length}',
  );

  // Test class-homework relationships
  final classIds = mockClassesList.map((c) => c.id).toSet();
  final homeworkClassIds = mockHomeworkList
      .where((hw) => hw.classId != null)
      .map((hw) => hw.classId!)
      .toSet();

  final validClassReferences = homeworkClassIds.intersection(classIds);
  debugPrint(
    'Valid class references: ${validClassReferences.length}/${homeworkClassIds.length}',
  );

  if (validClassReferences.length == homeworkClassIds.length) {
    debugPrint('✅ All homework class references are valid');
  } else {
    debugPrint('⚠️ Some homework has invalid class references');
  }
}
