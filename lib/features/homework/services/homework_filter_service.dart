import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../../core/enums/homework/assignment_type.dart';
import '../controllers/unified_homework_controller.dart';
import '../models/homework_filter_config.dart';
import '../models/homework_model.dart';

/// Service for managing homework filters with extensible architecture
/// This service provides a centralized way to manage filters and makes it easy
/// to add new assignment types without major refactoring
class HomeworkFilterService {
  static final HomeworkFilterService _instance = HomeworkFilterService._internal();
  factory HomeworkFilterService() => _instance;
  HomeworkFilterService._internal();

  final Logger _logger = Logger();
  
  /// Registry of available filter configurations
  final Map<UnifiedHomeworkFilterType, HomeworkFilterConfig> _filterRegistry = {};
  
  /// Cache for filter counts to improve performance
  final Map<String, Map<UnifiedHomeworkFilterType, int>> _filterCountsCache = {};
  
  /// Initialize the service with default filters
  void initialize() {
    _logger.i('Initializing HomeworkFilterService');
    
    // Register default filters
    for (final config in HomeworkFilterConfig.defaultConfigs) {
      registerFilter(config);
    }
    
    _logger.i('Registered ${_filterRegistry.length} default filters');
  }
  
  /// Register a new filter configuration
  void registerFilter(HomeworkFilterConfig config) {
    _filterRegistry[config.type] = config;
    _logger.d('Registered filter: ${config.type} - ${config.label}');
  }
  
  /// Unregister a filter configuration
  void unregisterFilter(UnifiedHomeworkFilterType type) {
    _filterRegistry.remove(type);
    _logger.d('Unregistered filter: $type');
  }
  
  /// Get all registered filter configurations
  List<HomeworkFilterConfig> getAllFilters() {
    return _filterRegistry.values.toList();
  }
  
  /// Get a specific filter configuration
  HomeworkFilterConfig? getFilter(UnifiedHomeworkFilterType type) {
    return _filterRegistry[type];
  }
  
  /// Get the default filter
  HomeworkFilterConfig getDefaultFilter() {
    final defaultFilter = _filterRegistry.values
        .where((config) => config.isDefault)
        .firstOrNull;
    
    return defaultFilter ?? _filterRegistry.values.first;
  }
  
  /// Apply filters to a list of homework
  List<HomeworkModel> applyFilters(
    List<HomeworkModel> homework,
    UnifiedHomeworkFilterType filterType,
    String? classId,
  ) {
    _logger.d('Applying filter: $filterType, classId: $classId');
    
    final config = getFilter(filterType);
    if (config == null) {
      _logger.w('Unknown filter type: $filterType');
      return homework;
    }
    
    List<HomeworkModel> filtered = List.from(homework);
    
    // Apply assignment type filter
    if (config.assignmentType != null) {
      filtered = filtered
          .where((hw) => hw.assignmentType == config.assignmentType)
          .toList();
    }
    
    // Apply class filter if relevant
    if (classId != null && filterType == UnifiedHomeworkFilterType.classAssignments) {
      filtered = filtered
          .where((hw) => hw.classId == classId)
          .toList();
    }
    
    _logger.d('Filter result: ${filtered.length} items');
    return filtered;
  }
  
  /// Calculate filter counts for a list of homework
  Map<UnifiedHomeworkFilterType, int> calculateFilterCounts(
    List<HomeworkModel> homework,
    {String? cacheKey}
  ) {
    // Check cache first
    if (cacheKey != null && _filterCountsCache.containsKey(cacheKey)) {
      return _filterCountsCache[cacheKey]!;
    }
    
    final counts = <UnifiedHomeworkFilterType, int>{};
    
    for (final config in _filterRegistry.values) {
      if (config.assignmentType == null) {
        // "All" filter
        counts[config.type] = homework.length;
      } else {
        // Specific assignment type filter
        counts[config.type] = homework
            .where((hw) => hw.assignmentType == config.assignmentType)
            .length;
      }
    }
    
    // Cache the result
    if (cacheKey != null) {
      _filterCountsCache[cacheKey] = counts;
    }
    
    return counts;
  }
  
  /// Clear filter counts cache
  void clearCache() {
    _filterCountsCache.clear();
    _logger.d('Cleared filter counts cache');
  }
  
  /// Get filters that have content (count > 0)
  List<UnifiedHomeworkFilterType> getFiltersWithContent(
    Map<UnifiedHomeworkFilterType, int> counts,
  ) {
    return counts.entries
        .where((entry) => entry.value > 0)
        .map((entry) => entry.key)
        .toList();
  }
  
  /// Get recommended filters based on current selection and available content
  List<UnifiedHomeworkFilterType> getRecommendedFilters(
    UnifiedHomeworkFilterType current,
    Map<UnifiedHomeworkFilterType, int> counts,
  ) {
    final recommendations = <UnifiedHomeworkFilterType>[];
    
    if (current == UnifiedHomeworkFilterType.all) {
      // If viewing all, recommend specific types with content
      for (final entry in counts.entries) {
        if (entry.key != UnifiedHomeworkFilterType.all && entry.value > 0) {
          recommendations.add(entry.key);
        }
      }
    } else {
      // If viewing specific type, recommend "All" and other types with content
      recommendations.add(UnifiedHomeworkFilterType.all);
      for (final entry in counts.entries) {
        if (entry.key != current && 
            entry.key != UnifiedHomeworkFilterType.all && 
            entry.value > 0) {
          recommendations.add(entry.key);
        }
      }
    }
    
    return recommendations;
  }
  
  /// Validate if a filter transition is allowed
  bool canTransitionTo(
    UnifiedHomeworkFilterType from,
    UnifiedHomeworkFilterType to,
  ) {
    // Check if both filters are registered
    if (!_filterRegistry.containsKey(from) || !_filterRegistry.containsKey(to)) {
      return false;
    }
    
    // All transitions are allowed in the current implementation
    // This method can be extended for complex business rules
    return true;
  }
  
  /// Get filter analytics data
  Map<String, dynamic> getFilterAnalytics(
    UnifiedHomeworkFilterType filterType,
    int itemCount,
    Duration loadTime,
  ) {
    final config = getFilter(filterType);
    
    return {
      'filter_type': filterType.toString(),
      'filter_label': config?.label ?? 'Unknown',
      'item_count': itemCount,
      'load_time_ms': loadTime.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
      'has_assignment_type': config?.assignmentType != null,
    };
  }
  
  /// Register a new assignment type filter dynamically
  /// This method allows adding new assignment types without code changes
  void registerCustomAssignmentTypeFilter({
    required String id,
    required String label,
    required String shortLabel,
    required String description,
    required AssignmentType assignmentType,
    IconData icon = Icons.assignment,
    Color color = Colors.blue,
  }) {
    // Create a new filter type dynamically (this would require enum extension in real implementation)
    _logger.i('Registering custom assignment type filter: $id');
    
    // For now, log the registration - in a real implementation, 
    // you might use a different approach like string-based filter types
    _logger.w('Custom filter registration not fully implemented - requires enum extension');
  }
  
  /// Get filter statistics
  Map<String, dynamic> getFilterStatistics() {
    return {
      'total_filters': _filterRegistry.length,
      'cache_entries': _filterCountsCache.length,
      'registered_filters': _filterRegistry.keys.map((k) => k.toString()).toList(),
    };
  }
  
  /// Reset the service to default state
  void reset() {
    _filterRegistry.clear();
    _filterCountsCache.clear();
    initialize();
    _logger.i('Reset HomeworkFilterService to default state');
  }
}

/// Extension methods for easier filter service usage
extension HomeworkFilterServiceExtension on List<HomeworkModel> {
  /// Apply filters using the filter service
  List<HomeworkModel> applyHomeworkFilters(
    UnifiedHomeworkFilterType filterType,
    String? classId,
  ) {
    return HomeworkFilterService().applyFilters(this, filterType, classId);
  }
  
  /// Calculate filter counts using the filter service
  Map<UnifiedHomeworkFilterType, int> calculateHomeworkFilterCounts({
    String? cacheKey,
  }) {
    return HomeworkFilterService().calculateFilterCounts(this, cacheKey: cacheKey);
  }
}

/// Singleton instance for global access
final homeworkFilterService = HomeworkFilterService();
