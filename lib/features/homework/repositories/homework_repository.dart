import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../core/enums/homework/assignment_type.dart';
import '../models/homework_model.dart';
import '../models/homework_submission_model.dart';

/// Repository for managing homework data with Firebase Firestore
class HomeworkRepository {
  static final HomeworkRepository _instance = HomeworkRepository._internal();
  factory HomeworkRepository() => _instance;
  HomeworkRepository._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Collection names
  static const String _homeworkCollection = 'homeworks';
  static const String _submissionsCollection = 'homework_submissions';

  /// Fetch all homework assigned for a specific date
  Future<List<HomeworkModel>> getHomeworkForDate(DateTime date) async {
    try {
      _logger.i('Fetching homework for date: ${date.toIso8601String()}');

      // Create start and end of day timestamps for the query
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .where(
            'assignedAt',
            isGreaterThanOrEqualTo: startOfDay.toIso8601String(),
          )
          .where('assignedAt', isLessThanOrEqualTo: endOfDay.toIso8601String())
          .orderBy('assignedAt', descending: true)
          .get();

      final homeworkList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // Ensure the document ID is included
              return HomeworkModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing homework document ${doc.id}: $e');
              return null;
            }
          })
          .where((homework) => homework != null)
          .cast<HomeworkModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${homeworkList.length} homework items for date',
      );
      return homeworkList;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching homework for date: ${e.message}');
      throw Exception('Failed to fetch homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching homework for date: $e');
      throw Exception('Failed to fetch homework: $e');
    }
  }

  /// Fetch a single homework by document ID
  Future<HomeworkModel?> getHomeworkById(String homeworkId) async {
    try {
      _logger.i('Fetching homework by ID: $homeworkId');

      final docSnapshot = await _firestore
          .collection(_homeworkCollection)
          .doc(homeworkId)
          .get();

      if (!docSnapshot.exists) {
        _logger.w('Homework not found with ID: $homeworkId');
        return null;
      }

      final data = docSnapshot.data()!;
      data['id'] = docSnapshot.id; // Ensure the document ID is included

      final homework = HomeworkModel.fromJson(data);
      _logger.i('Successfully fetched homework: $homeworkId');
      return homework;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching homework by ID: ${e.message}');
      throw Exception('Failed to fetch homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching homework by ID: $e');
      throw Exception('Failed to fetch homework: $e');
    }
  }

  /// Fetch a student's submission for a given homework
  Future<HomeworkSubmissionModel?> getSubmission(
    String homeworkId,
    String userId,
  ) async {
    try {
      _logger.i('Fetching submission for homework: $homeworkId, user: $userId');

      // First, get the homework to find the submission ID
      final homework = await getHomeworkById(homeworkId);
      if (homework?.submissionId == null) {
        _logger.i('No submission ID found in homework: $homeworkId');
        return null;
      }

      final submissionId = homework!.submissionId!;
      final docSnapshot = await _firestore
          .collection(_submissionsCollection)
          .doc(submissionId)
          .get();

      if (!docSnapshot.exists) {
        _logger.i('No submission found with ID: $submissionId');
        return null;
      }

      final submissionData = docSnapshot.data()!;
      // Verify this submission belongs to the correct user and homework
      if (submissionData['userId'] != userId ||
          submissionData['homeworkId'] != homeworkId) {
        _logger.w(
          'Submission $submissionId does not belong to user $userId or homework $homeworkId',
        );
        return null;
      }

      final submission = HomeworkSubmissionModel.fromJson(submissionData);
      _logger.i('Successfully fetched submission: $submissionId');
      return submission;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching submission: ${e.message}');
      throw Exception('Failed to fetch submission: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching submission: $e');
      throw Exception('Failed to fetch submission: $e');
    }
  }

  /// Create or update a homework submission
  Future<void> submitHomework(HomeworkSubmissionModel submission) async {
    try {
      _logger.i(
        'Submitting homework: ${submission.homeworkId} for user: ${submission.userId}',
      );

      // Use the submission's ID (should be unique)
      final submissionId = submission.id;

      // Save the submission
      await _firestore
          .collection(_submissionsCollection)
          .doc(submissionId)
          .set(submission.toJson());

      // Update the homework document to reference this submission
      await _firestore
          .collection(_homeworkCollection)
          .doc(submission.homeworkId)
          .update({'submissionId': submissionId});

      _logger.i('Successfully submitted homework with ID: $submissionId');
    } on FirebaseException catch (e) {
      _logger.e('Firebase error submitting homework: ${e.message}');
      throw Exception('Failed to submit homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error submitting homework: $e');
      throw Exception('Failed to submit homework: $e');
    }
  }

  /// Mark homework as done/undone for homework that doesn't require submission
  Future<void> markHomeworkDone(
    String homeworkId,
    String userId,
    bool isDone,
  ) async {
    try {
      _logger.i(
        'Marking homework ${isDone ? 'done' : 'undone'} for user $userId: $homeworkId',
      );

      // Update the homework document with user-specific status
      await _firestore.collection(_homeworkCollection).doc(homeworkId).update({
        'userStatus.$userId': isDone ? 'done' : 'pending',
        'updatedAt': DateTime.now().toIso8601String(),
      });

      _logger.i(
        'Successfully marked homework ${isDone ? 'done' : 'undone'} for user $userId',
      );
    } on FirebaseException catch (e) {
      _logger.e('Firebase error marking homework done: ${e.message}');
      throw Exception('Failed to mark homework done: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error marking homework done: $e');
      throw Exception('Failed to mark homework done: $e');
    }
  }

  /// Get homework status (done/undone) for homework that doesn't require submission
  Future<bool> getHomeworkStatus(String homeworkId, String userId) async {
    try {
      _logger.i('Fetching homework status for user $userId: $homeworkId');

      final docSnapshot = await _firestore
          .collection(_homeworkCollection)
          .doc(homeworkId)
          .get();

      if (!docSnapshot.exists) {
        _logger.w('Homework not found: $homeworkId');
        return false;
      }

      final data = docSnapshot.data()!;
      final userStatus = data['userStatus'] as Map<String, dynamic>?;
      final status = userStatus?[userId] as String?;
      final isDone = status == 'done';

      _logger.i(
        'Successfully fetched homework status for user $userId: $isDone',
      );
      return isDone;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching homework status: ${e.message}');
      throw Exception('Failed to fetch homework status: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching homework status: $e');
      throw Exception('Failed to fetch homework status: $e');
    }
  }

  /// Get all homework for a date range (useful for date selector)
  Future<List<HomeworkModel>> getHomeworkForDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      _logger.i(
        'Fetching homework for date range: ${startDate.toIso8601String()} to ${endDate.toIso8601String()}',
      );

      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .where(
            'assignedAt',
            isGreaterThanOrEqualTo: startDate.toIso8601String(),
          )
          .where('assignedAt', isLessThanOrEqualTo: endDate.toIso8601String())
          .orderBy('assignedAt', descending: true)
          .get();

      final homeworkList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // Ensure the document ID is included
              return HomeworkModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing homework document ${doc.id}: $e');
              return null;
            }
          })
          .where((homework) => homework != null)
          .cast<HomeworkModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${homeworkList.length} homework items for date range',
      );
      return homeworkList;
    } on FirebaseException catch (e) {
      _logger.e(
        'Firebase error fetching homework for date range: ${e.message}',
      );
      throw Exception('Failed to fetch homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching homework for date range: $e');
      throw Exception('Failed to fetch homework: $e');
    }
  }

  /// Get all homework (useful for "All" filter)
  Future<List<HomeworkModel>> getAllHomework() async {
    try {
      _logger.i('Fetching all homework');

      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .orderBy('assignedAt', descending: true)
          .get();

      final homeworkList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // Ensure the document ID is included
              return HomeworkModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing homework document ${doc.id}: $e');
              return null;
            }
          })
          .where((homework) => homework != null)
          .cast<HomeworkModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${homeworkList.length} total homework items',
      );
      return homeworkList;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching all homework: ${e.message}');
      throw Exception('Failed to fetch homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching all homework: $e');
      throw Exception('Failed to fetch homework: $e');
    }
  }

  /// Fetch homework for a specific user (includes both class and individual assignments)
  /// This method provides unified loading by fetching all assignment types simultaneously
  Future<List<HomeworkModel>> getHomeworkForUser(String userId) async {
    try {
      _logger.i('Fetching unified homework for user: $userId');

      // Use Future.wait to fetch all assignment types simultaneously for better performance
      final results = await Future.wait([
        getHomeworkByAssignmentType(AssignmentType.classAssignment),
        getIndividualHomeworkForUser(userId),
        getGroupHomeworkForUser(userId),
      ]);

      // Combine all results
      final List<HomeworkModel> userHomework = [];
      for (final result in results) {
        userHomework.addAll(result);
      }

      // Remove duplicates (in case there are any) and sort by assignedAt
      final uniqueHomework = <String, HomeworkModel>{};
      for (final homework in userHomework) {
        uniqueHomework[homework.id] = homework;
      }

      final finalHomework = uniqueHomework.values.toList();
      finalHomework.sort((a, b) => b.assignedAt.compareTo(a.assignedAt));

      _logger.i(
        'Successfully fetched ${finalHomework.length} unified homework items for user $userId',
      );
      return finalHomework;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching homework for user: ${e.message}');
      throw Exception('Failed to fetch homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching homework for user: $e');
      throw Exception('Failed to fetch homework: $e');
    }
  }

  /// Fetch homework for a specific user filtered by date
  Future<List<HomeworkModel>> getHomeworkForUserByDate(
    String userId,
    DateTime date,
  ) async {
    try {
      _logger.i(
        'Fetching homework for user: $userId on date: ${date.toIso8601String()}',
      );

      // Get all user homework first
      final allUserHomework = await getHomeworkForUser(userId);

      // Filter by the specific date
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

      final filteredHomework = allUserHomework.where((homework) {
        return homework.assignedAt.isAfter(
              startOfDay.subtract(const Duration(seconds: 1)),
            ) &&
            homework.assignedAt.isBefore(
              endOfDay.add(const Duration(seconds: 1)),
            );
      }).toList();

      _logger.i(
        'Successfully fetched ${filteredHomework.length} homework items for user $userId on date ${date.toIso8601String()}',
      );
      return filteredHomework;
    } catch (e) {
      _logger.e('Error fetching homework for user by date: $e');
      rethrow;
    }
  }

  /// Fetch homework by assignment type
  Future<List<HomeworkModel>> getHomeworkByAssignmentType(
    AssignmentType assignmentType,
  ) async {
    try {
      _logger.i('Fetching homework by assignment type: ${assignmentType.name}');

      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .where('assignmentType', isEqualTo: assignmentType.name)
          .orderBy('assignedAt', descending: true)
          .get();

      final homeworkList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return HomeworkModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing homework document ${doc.id}: $e');
              return null;
            }
          })
          .where((homework) => homework != null)
          .cast<HomeworkModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${homeworkList.length} homework items for assignment type ${assignmentType.name}',
      );
      return homeworkList;
    } catch (e) {
      _logger.e('Error fetching homework by assignment type: $e');
      rethrow;
    }
  }

  /// Fetch homework for a specific class
  Future<List<HomeworkModel>> getHomeworkForClass(String classId) async {
    try {
      _logger.i('Fetching homework for class: $classId');

      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .where('classId', isEqualTo: classId)
          .where(
            'assignmentType',
            isEqualTo: AssignmentType.classAssignment.name,
          )
          .orderBy('assignedAt', descending: true)
          .get();

      final homeworkList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return HomeworkModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing homework document ${doc.id}: $e');
              return null;
            }
          })
          .where((homework) => homework != null)
          .cast<HomeworkModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${homeworkList.length} homework items for class $classId',
      );
      return homeworkList;
    } catch (e) {
      _logger.e('Error fetching homework for class: $e');
      rethrow;
    }
  }

  /// Fetch individual assignments for a specific user
  Future<List<HomeworkModel>> getIndividualHomeworkForUser(
    String userId,
  ) async {
    try {
      _logger.i('Fetching individual homework for user: $userId');

      // Use simpler query to avoid composite index requirement
      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .where('assignmentType', isEqualTo: AssignmentType.individual.name)
          .where('assignedUserIds', arrayContains: userId)
          .get();

      final homeworkList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return HomeworkModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing homework document ${doc.id}: $e');
              return null;
            }
          })
          .where((homework) => homework != null)
          .cast<HomeworkModel>()
          .toList();

      // Sort by assignedAt in memory since we can't use orderBy with array-contains
      homeworkList.sort((a, b) => b.assignedAt.compareTo(a.assignedAt));

      _logger.i(
        'Successfully fetched ${homeworkList.length} individual homework items for user $userId',
      );
      return homeworkList;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching individual homework: ${e.message}');
      throw Exception('Failed to fetch individual homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching individual homework: $e');
      throw Exception('Failed to fetch individual homework: $e');
    }
  }

  /// Fetch group assignments for a specific user
  Future<List<HomeworkModel>> getGroupHomeworkForUser(String userId) async {
    try {
      _logger.i('Fetching group homework for user: $userId');

      // Use simpler query to avoid composite index requirement
      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .where('assignmentType', isEqualTo: AssignmentType.group.name)
          .where('assignedUserIds', arrayContains: userId)
          .get();

      final homeworkList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id;
              return HomeworkModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing homework document ${doc.id}: $e');
              return null;
            }
          })
          .where((homework) => homework != null)
          .cast<HomeworkModel>()
          .toList();

      // Sort by assignedAt in memory since we can't use orderBy with array-contains
      homeworkList.sort((a, b) => b.assignedAt.compareTo(a.assignedAt));

      _logger.i(
        'Successfully fetched ${homeworkList.length} group homework items for user $userId',
      );
      return homeworkList;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching group homework: ${e.message}');
      throw Exception('Failed to fetch group homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching group homework: $e');
      throw Exception('Failed to fetch group homework: $e');
    }
  }
}
