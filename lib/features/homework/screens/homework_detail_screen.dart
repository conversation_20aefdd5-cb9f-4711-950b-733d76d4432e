import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:scholara_student/core/routes/app_routes.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/enums/homework/submission_type.dart';
import '../controllers/homework_controller.dart';
import '../models/homework_model.dart';
import '../models/homework_submission_model.dart';
import '../widgets/general_info_section.dart';
import '../widgets/resource_list.dart';

/// Screen displaying detailed information about a homework assignment
class HomeworkDetailScreen extends ConsumerStatefulWidget {
  /// The ID of the homework to display
  final String homeworkId;

  const HomeworkDetailScreen({super.key, required this.homeworkId});

  @override
  ConsumerState<HomeworkDetailScreen> createState() =>
      _HomeworkDetailScreenState();
}

class _HomeworkDetailScreenState extends ConsumerState<HomeworkDetailScreen> {
  String? personalNotes;

  /// Show three-dot menu options
  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Symbols.share),
              title: const Text('Share'),
              onTap: () {
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Symbols.flag),
              title: const Text('Report Issue'),
              onTap: () {
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Show personal notes editor modal
  void _showNotesEditor() {
    final controller = TextEditingController(text: personalNotes ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Your Notes'),
        content: SizedBox(
          width: double.maxFinite,
          child: TextField(
            controller: controller,
            maxLines: 8,
            decoration: const InputDecoration(
              hintText: 'Add your personal notes here...',
              border: OutlineInputBorder(),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                personalNotes = controller.text.trim().isEmpty
                    ? null
                    : controller.text.trim();
              });
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  /// Handle action button press
  Future<void> _handleActionButton(
    HomeworkModel homework,
    HomeworkSubmissionModel? submission,
  ) async {
    if (homework.requiresSubmission &&
        homework.submissionType == SubmissionType.online) {
      // Online submission logic
      if (submission == null) {
        // No submission exists - navigate to submit screen
        context.pushNamed(
          RouteNames.submitHomework,
          pathParameters: {'id': homework.id},
        );
      } else {
        // Submission exists - navigate to view submission screen
        context.pushNamed(
          RouteNames.viewSubmission,
          pathParameters: {'id': homework.id},
        );
      }
    } else {
      // Offline submission or no submission required - toggle done status
      final currentStatus = await ref.read(
        homeworkStatusProvider(homework.id).future,
      );

      try {
        await ref.read(
          markCurrentUserHomeworkDoneProvider((
            homeworkId: homework.id,
            isDone: !currentStatus,
          )).future,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                currentStatus
                    ? 'Homework marked as undone!'
                    : 'Homework marked as done!',
              ),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: ${e.toString()}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  /// Get action button text based on homework state
  String _getActionButtonText(
    HomeworkModel homework,
    HomeworkSubmissionModel? submission,
    bool isDone,
  ) {
    if (homework.requiresSubmission &&
        homework.submissionType == SubmissionType.online) {
      // Online submission logic
      return submission == null ? 'Submit' : 'View Submission';
    } else {
      // Offline submission or no submission required
      return isDone ? 'Mark as Undone' : 'Mark as Done';
    }
  }

  /// Get action button icon based on homework state
  Icon _getActionButtonIcon(
    HomeworkModel homework,
    HomeworkSubmissionModel? submission,
    bool isDone,
  ) {
    if (homework.requiresSubmission &&
        homework.submissionType == SubmissionType.online) {
      // Online submission logic
      return submission == null
          ? const Icon(Symbols.upload)
          : const Icon(Symbols.visibility);
    } else {
      // Offline submission or no submission required
      return isDone ? const Icon(Symbols.undo) : const Icon(Symbols.check);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentUserId = ref.read(currentUserIdProvider);

    // Watch homework data
    final homeworkAsync = ref.watch(homeworkDetailProvider(widget.homeworkId));

    return Scaffold(
      appBar: AppBar(
        title: homeworkAsync.when(
          loading: () => const Text('Loading...'),
          error: (_, __) => const Text('Error'),
          data: (homework) => Text(
            homework?.subject ?? 'Homework',
            style: theme.textTheme.titleLarge,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showMoreOptions,
            icon: const Icon(Symbols.more_vert),
            tooltip: 'More options',
          ),
        ],
      ),
      body: homeworkAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => _ErrorState(
          error: error.toString(),
          onRetry: () =>
              ref.invalidate(homeworkDetailProvider(widget.homeworkId)),
        ),
        data: (homework) {
          if (homework == null) {
            return const _NotFoundState();
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GeneralInfoSection(homework: homework),
                      SizedBox(height: 24.h),

                      // Resources Section
                      if (homework.resourceUrls.isNotEmpty) ...[
                        ResourceList(resourceUrls: homework.resourceUrls),
                        SizedBox(height: 24.h),
                      ],

                      // Teacher Notes Section
                      if (homework.teacherNote != null) ...[
                        _TeacherNotesSection(note: homework.teacherNote!),
                        SizedBox(height: 24.h),
                      ],

                      // Personal Notes Section
                      _PersonalNotesSection(
                        notes: personalNotes,
                        onEditPressed: _showNotesEditor,
                      ),

                      // Add bottom padding to account for FAB
                      SizedBox(height: 80.h),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: homeworkAsync.when(
        loading: () => null,
        error: (_, __) => null,
        data: (homework) {
          if (homework == null) return null;

          final submissionAsync = ref.watch(
            submissionProvider((
              homeworkId: homework.id,
              userId: currentUserId,
            )),
          );

          final statusAsync = homework.requiresSubmission
              ? const AsyncValue.data(false)
              : ref.watch(homeworkStatusProvider(homework.id));

          return submissionAsync.when(
            loading: () => FloatingActionButton.extended(
              onPressed: null,
              icon: const CircularProgressIndicator(),
              label: const Text('Loading...'),
            ),
            error: (_, __) => FloatingActionButton.extended(
              onPressed: () => ref.invalidate(
                submissionProvider((
                  homeworkId: homework.id,
                  userId: currentUserId,
                )),
              ),
              icon: const Icon(Symbols.refresh),
              label: const Text('Retry'),
            ),
            data: (submission) {
              return statusAsync.when(
                loading: () => FloatingActionButton.extended(
                  onPressed: null,
                  icon: const CircularProgressIndicator(),
                  label: const Text('Loading...'),
                ),
                error: (_, __) => FloatingActionButton.extended(
                  onPressed: () =>
                      ref.invalidate(homeworkStatusProvider(homework.id)),
                  icon: const Icon(Symbols.refresh),
                  label: const Text('Retry'),
                ),
                data: (isDone) {
                  return FloatingActionButton.extended(
                    onPressed: () => _handleActionButton(homework, submission),
                    icon: _getActionButtonIcon(homework, submission, isDone),
                    label: Text(
                      _getActionButtonText(homework, submission, isDone),
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}

/// Teacher notes section widget
class _TeacherNotesSection extends StatelessWidget {
  final String note;

  const _TeacherNotesSection({required this.note});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Teacher Notes',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(note, style: theme.textTheme.bodyMedium),
        ],
      ),
    );
  }
}

/// Personal notes section widget
class _PersonalNotesSection extends StatelessWidget {
  final String? notes;
  final VoidCallback onEditPressed;

  const _PersonalNotesSection({
    required this.notes,
    required this.onEditPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Your Notes',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                onPressed: onEditPressed,
                child: Text(notes == null ? 'Add' : 'Edit'),
              ),
            ],
          ),
          if (notes != null) ...[
            SizedBox(height: 8.h),
            Text(notes!, style: theme.textTheme.bodyMedium),
          ] else ...[
            SizedBox(height: 8.h),
            Text(
              'No personal notes added yet.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Error state widget when homework loading fails
class _ErrorState extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;

  const _ErrorState({required this.error, required this.onRetry});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Symbols.error, size: 64.sp, color: theme.colorScheme.error),
            SizedBox(height: 16.h),
            Text(
              'Failed to load homework',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              error,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Symbols.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Not found state widget when homework doesn't exist
class _NotFoundState extends StatelessWidget {
  const _NotFoundState();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Symbols.search_off,
              size: 64.sp,
              color: isDark
                  ? AppColors.textSecondaryDark
                  : AppColors.textSecondaryLight,
            ),
            SizedBox(height: 16.h),
            Text(
              'Homework not found',
              style: theme.textTheme.titleMedium?.copyWith(
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'The homework you\'re looking for doesn\'t exist or has been removed.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
