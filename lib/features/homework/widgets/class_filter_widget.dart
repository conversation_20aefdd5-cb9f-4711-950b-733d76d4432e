import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/providers/auth_providers.dart';
import '../controllers/class_controller.dart';
import '../controllers/unified_homework_controller.dart';

/// Class filter widget for selecting specific classes
class ClassFilterWidget extends ConsumerWidget {
  const ClassFilterWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final selectedClassId = ref.watch(classFilterProvider);
    final filterType = ref.watch(unifiedHomeworkFilterProvider);
    final classroomContext = ref.watch(classroomContextProvider);

    // Only show class filter when class assignments filter is selected
    // and not in classroom context (where filter is fixed)
    if (filterType != UnifiedHomeworkFilterType.classAssignments ||
        classroomContext != null) {
      return const SizedBox.shrink();
    }

    // Get current user ID for fetching their classes
    final authState = ref.watch(authStateProvider);
    final userId = authState.when(
      data: (state) => state.user?.id,
      loading: () => null,
      error: (_, __) => null,
    );

    if (userId == null) {
      return const SizedBox.shrink();
    }

    final classesAsync = ref.watch(studentClassesProvider(userId));

    return Container(
      height: 45.h,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filter by Class',
            style: theme.textTheme.labelMedium?.copyWith(
              color: isDark
                  ? AppColors.textSecondaryDark
                  : AppColors.textSecondaryLight,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Expanded(
            child: classesAsync.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stackTrace) => Center(
                child: Text(
                  'Error loading classes',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isDark
                        ? AppColors.textSecondaryDark
                        : AppColors.textSecondaryLight,
                  ),
                ),
              ),
              data: (classes) {
                if (classes.isEmpty) {
                  return Center(
                    child: Text(
                      'No classes found',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isDark
                            ? AppColors.textSecondaryDark
                            : AppColors.textSecondaryLight,
                      ),
                    ),
                  );
                }

                return ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: classes.length + 1, // +1 for "All Classes" option
                  separatorBuilder: (context, index) => SizedBox(width: 8.w),
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      // "All Classes" option
                      return _ClassChip(
                        classId: null,
                        className: 'All Classes',
                        isSelected: selectedClassId == null,
                        onTap: () {
                          ref.read(classFilterProvider.notifier).state = null;
                        },
                      );
                    }

                    final classModel = classes[index - 1];
                    return _ClassChip(
                      classId: classModel.id,
                      className: classModel.name,
                      isSelected: selectedClassId == classModel.id,
                      onTap: () {
                        ref.read(classFilterProvider.notifier).state =
                            classModel.id;
                      },
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// Individual class chip widget
class _ClassChip extends StatelessWidget {
  final String? classId;
  final String className;
  final bool isSelected;
  final VoidCallback onTap;

  const _ClassChip({
    required this.classId,
    required this.className,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primary.withValues(alpha: 0.15)
              : (isDark
                    ? AppColors.surfaceDark.withValues(alpha: 0.5)
                    : AppColors.surfaceLight.withValues(alpha: 0.5)),
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : (isDark ? AppColors.borderDark : AppColors.borderLight),
            width: isSelected ? 1.5 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              classId == null ? Symbols.select_all : Symbols.school,
              size: 14.sp,
              color: isSelected
                  ? theme.colorScheme.primary
                  : (isDark
                        ? AppColors.textSecondaryDark
                        : AppColors.textSecondaryLight),
            ),
            SizedBox(width: 6.w),
            Text(
              className,
              style: theme.textTheme.labelSmall?.copyWith(
                color: isSelected
                    ? theme.colorScheme.primary
                    : (isDark
                          ? AppColors.textPrimaryDark
                          : AppColors.textPrimaryLight),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
