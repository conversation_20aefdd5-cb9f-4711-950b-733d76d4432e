import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/enums/homework/assignment_type.dart';
import '../../../core/enums/homework/homework_status.dart';
import '../../../core/enums/homework/submission_type.dart';
import '../controllers/class_controller.dart';
import '../models/homework_model.dart';

/// Card widget to display homework information
class HomeworkCard extends ConsumerWidget {
  /// The homework model to display
  final HomeworkModel homework;

  const HomeworkCard({super.key, required this.homework});

  /// Format due time for display
  String _formatDueTime(DateTime? dueAt, HomeworkStatus status) {
    if (dueAt == null) return 'No due date';

    final now = DateTime.now();
    final difference = dueAt.difference(now);

    // Only show overdue for pending or rejected status
    if (difference.isNegative &&
        (status == HomeworkStatus.pending ||
            status == HomeworkStatus.rejected)) {
      return 'Overdue';
    } else if (difference.isNegative) {
      // For other statuses, just show the due date without "overdue"
      return 'Due date passed';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} left';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} left';
    } else {
      return 'Due soon';
    }
  }

  /// Get color for due time based on urgency
  Color _getDueTimeColor(DateTime? dueAt, HomeworkStatus status, bool isDark) {
    if (dueAt == null) {
      return isDark
          ? AppColors.textSecondaryDark
          : AppColors.textSecondaryLight;
    }

    final now = DateTime.now();
    final difference = dueAt.difference(now);

    // Only show error color for overdue when status is pending or rejected
    if (difference.isNegative &&
        (status == HomeworkStatus.pending ||
            status == HomeworkStatus.rejected)) {
      return isDark ? AppColors.errorDark : AppColors.errorLight;
    } else if (difference.isNegative) {
      // For other statuses, use normal text color
      return isDark
          ? AppColors.textSecondaryDark
          : AppColors.textSecondaryLight;
    } else if (difference.inDays <= 1) {
      return isDark ? AppColors.warningDark : AppColors.warningLight;
    } else {
      return isDark
          ? AppColors.textSecondaryDark
          : AppColors.textSecondaryLight;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: () {
          context.pushNamed(
            RouteNames.homeworkDetail,
            pathParameters: {'id': homework.id},
          );
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with subject and submission type
              Row(
                children: [
                  // Subject chip
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    child: Text(
                      homework.subject,
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  SizedBox(width: 8.w),

                  // Assignment type chip
                  _AssignmentTypeChip(homework: homework, ref: ref),

                  const Spacer(),
                  // Submission type icon
                  Icon(
                    homework.submissionType.requiresFileUpload
                        ? Symbols.cloud_upload
                        : Symbols.assignment_turned_in,
                    size: 20.sp,
                    color: isDark
                        ? AppColors.textSecondaryDark
                        : AppColors.textSecondaryLight,
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Title
              Text(
                homework.title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              // Description (if available)
              if (homework.description != null) ...[
                SizedBox(height: 8.h),
                Text(
                  homework.description!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isDark
                        ? AppColors.textSecondaryDark
                        : AppColors.textSecondaryLight,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              SizedBox(height: 12.h),

              // Bottom row with due time and status
              Row(
                children: [
                  // Due time
                  Row(
                    children: [
                      Icon(
                        Symbols.schedule,
                        size: 16.sp,
                        color: _getDueTimeColor(
                          homework.dueAt,
                          homework.status,
                          isDark,
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        _formatDueTime(homework.dueAt, homework.status),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: _getDueTimeColor(
                            homework.dueAt,
                            homework.status,
                            isDark,
                          ),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),

                  const Spacer(),

                  // Status chip
                  _StatusChip(status: homework.status),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Status chip widget to display homework status
class _StatusChip extends StatelessWidget {
  final HomeworkStatus status;

  const _StatusChip({required this.status});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: status.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(
          color: status.color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        status.label,
        style: theme.textTheme.labelSmall?.copyWith(
          color: status.color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

/// Assignment type chip widget to display assignment context
class _AssignmentTypeChip extends ConsumerWidget {
  final HomeworkModel homework;
  final WidgetRef ref;

  const _AssignmentTypeChip({required this.homework, required this.ref});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Get assignment type info
    final assignmentType = homework.assignmentType;

    // For class assignments, try to get class name
    if (assignmentType == AssignmentType.classAssignment &&
        homework.classId != null) {
      final classAsync = ref.watch(classDetailProvider(homework.classId!));

      return classAsync.when(
        loading: () => _buildChip(
          context,
          assignmentType.shortLabel,
          assignmentType.color,
          isDark,
        ),
        error: (_, __) => _buildChip(
          context,
          assignmentType.shortLabel,
          assignmentType.color,
          isDark,
        ),
        data: (classModel) => _buildChip(
          context,
          classModel?.name ?? assignmentType.shortLabel,
          assignmentType.color,
          isDark,
        ),
      );
    }

    // For individual/group assignments, show assignment type
    return _buildChip(
      context,
      assignmentType.shortLabel,
      assignmentType.color,
      isDark,
    );
  }

  Widget _buildChip(
    BuildContext context,
    String label,
    Color color,
    bool isDark,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4.r),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(homework.assignmentType.icon, size: 12.sp, color: color),
          SizedBox(width: 2.w),
          Text(
            label,
            style: theme.textTheme.labelSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 10.sp,
            ),
          ),
        ],
      ),
    );
  }
}
