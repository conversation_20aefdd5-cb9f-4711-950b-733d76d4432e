import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/theme/app_colors.dart';
import '../controllers/homework_filter_controller.dart';

/// Filter bar widget for assignment type selection
class AssignmentTypeFilterBar extends ConsumerWidget {
  const AssignmentTypeFilterBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final selectedFilter = ref.watch(homeworkFilterProvider);
    final filterOptionsAsync = ref.watch(filterOptionsProvider);

    return Container(
      height: 50.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: filterOptionsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => Center(
          child: Text(
            'Error loading filters',
            style: theme.textTheme.bodySmall?.copyWith(
              color: isDark
                  ? AppColors.textSecondaryDark
                  : AppColors.textSecondaryLight,
            ),
          ),
        ),
        data: (filterCounts) => ListView.separated(
          scrollDirection: Axis.horizontal,
          itemCount: HomeworkFilterType.values.length,
          separatorBuilder: (context, index) => SizedBox(width: 8.w),
          itemBuilder: (context, index) {
            final filterType = HomeworkFilterType.values[index];
            final count = filterCounts[filterType] ?? 0;
            final isSelected = selectedFilter == filterType;

            return _FilterChip(
              filterType: filterType,
              count: count,
              isSelected: isSelected,
              onTap: () {
                ref.read(homeworkFilterProvider.notifier).setFilter(filterType);
              },
            );
          },
        ),
      ),
    );
  }
}

/// Individual filter chip widget
class _FilterChip extends StatelessWidget {
  final HomeworkFilterType filterType;
  final int count;
  final bool isSelected;
  final VoidCallback onTap;

  const _FilterChip({
    required this.filterType,
    required this.count,
    required this.isSelected,
    required this.onTap,
  });

  IconData _getFilterIcon(HomeworkFilterType filterType) {
    switch (filterType) {
      case HomeworkFilterType.all:
        return Symbols.list;
      case HomeworkFilterType.classAssignments:
        return Symbols.school;
      case HomeworkFilterType.individual:
        return Symbols.person;
      case HomeworkFilterType.group:
        return Symbols.group;
    }
  }

  Color _getFilterColor(HomeworkFilterType filterType, ThemeData theme) {
    switch (filterType) {
      case HomeworkFilterType.all:
        return theme.colorScheme.primary;
      case HomeworkFilterType.classAssignments:
        return Colors.blue;
      case HomeworkFilterType.individual:
        return Colors.green;
      case HomeworkFilterType.group:
        return Colors.orange;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final filterColor = _getFilterColor(filterType, theme);

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected
              ? filterColor.withValues(alpha: 0.15)
              : (isDark
                  ? AppColors.surfaceDark.withValues(alpha: 0.5)
                  : AppColors.surfaceLight.withValues(alpha: 0.5)),
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: isSelected
                ? filterColor
                : (isDark
                    ? AppColors.borderDark
                    : AppColors.borderLight),
            width: isSelected ? 1.5 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getFilterIcon(filterType),
              size: 16.sp,
              color: isSelected
                  ? filterColor
                  : (isDark
                      ? AppColors.textSecondaryDark
                      : AppColors.textSecondaryLight),
            ),
            SizedBox(width: 6.w),
            Text(
              filterType.shortLabel,
              style: theme.textTheme.labelMedium?.copyWith(
                color: isSelected
                    ? filterColor
                    : (isDark
                        ? AppColors.textPrimaryDark
                        : AppColors.textPrimaryLight),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
            if (count > 0) ...[
              SizedBox(width: 4.w),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: isSelected
                      ? filterColor
                      : (isDark
                          ? AppColors.textSecondaryDark
                          : AppColors.textSecondaryLight),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Text(
                  count.toString(),
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: isSelected
                        ? Colors.white
                        : (isDark
                            ? AppColors.backgroundDark
                            : AppColors.backgroundLight),
                    fontWeight: FontWeight.w600,
                    fontSize: 10.sp,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
