import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/enums/homework/assignment_type.dart';
import '../../../core/enums/homework/homework_status.dart';
import '../../../core/enums/homework/submission_type.dart';
import '../controllers/class_controller.dart';
import '../models/homework_model.dart';

/// Widget displaying general information about a homework assignment
class GeneralInfoSection extends ConsumerWidget {
  /// The homework model to display
  final HomeworkModel homework;

  const GeneralInfoSection({super.key, required this.homework});

  /// Format date for display
  String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dateDay = DateTime(date.year, date.month, date.day);

    if (dateDay == today) {
      return 'Today at ${_formatTime(date)}';
    } else if (dateDay == today.subtract(const Duration(days: 1))) {
      return 'Yesterday at ${_formatTime(date)}';
    } else if (dateDay == today.add(const Duration(days: 1))) {
      return 'Tomorrow at ${_formatTime(date)}';
    } else {
      return '${months[date.month - 1]} ${date.day}, ${date.year} at ${_formatTime(date)}';
    }
  }

  /// Format time for display
  String _formatTime(DateTime date) {
    final hour = date.hour == 0
        ? 12
        : (date.hour > 12 ? date.hour - 12 : date.hour);
    final minute = date.minute.toString().padLeft(2, '0');
    final period = date.hour >= 12 ? 'PM' : 'AM';
    return '$hour:$minute $period';
  }

  /// Get due date display text
  String _getDueDateText(DateTime? dueAt, HomeworkStatus status) {
    if (dueAt == null) return 'No due date';

    final now = DateTime.now();
    final difference = dueAt.difference(now);

    if (difference.isNegative) {
      final daysPast = now.difference(dueAt).inDays;

      // Only show overdue language for pending or rejected status
      if (status == HomeworkStatus.pending ||
          status == HomeworkStatus.rejected) {
        if (daysPast == 0) {
          return 'Overdue (due today at ${_formatTime(dueAt)})';
        } else if (daysPast == 1) {
          return 'Overdue (due yesterday)';
        } else {
          return 'Overdue (due $daysPast days ago)';
        }
      } else {
        // For other statuses, just show when it was due
        if (daysPast == 0) {
          return 'Due today at ${_formatTime(dueAt)}';
        } else if (daysPast == 1) {
          return 'Due yesterday';
        } else {
          return 'Due $daysPast days ago';
        }
      }
    } else {
      final daysLeft = difference.inDays;
      if (daysLeft == 0) {
        return 'Due today at ${_formatTime(dueAt)}';
      } else if (daysLeft == 1) {
        return 'Due tomorrow at ${_formatTime(dueAt)}';
      } else {
        return 'Due in $daysLeft days';
      }
    }
  }

  /// Get due date color based on urgency
  Color _getDueDateColor(DateTime? dueAt, bool isDark) {
    if (dueAt == null) {
      return isDark
          ? AppColors.textSecondaryDark
          : AppColors.textSecondaryLight;
    }

    final now = DateTime.now();
    final difference = dueAt.difference(now);

    if (difference.isNegative) {
      return isDark ? AppColors.errorDark : AppColors.errorLight;
    } else if (difference.inDays <= 1) {
      return isDark ? AppColors.warningDark : AppColors.warningLight;
    } else {
      return isDark
          ? AppColors.textSecondaryDark
          : AppColors.textSecondaryLight;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          homework.title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),

        SizedBox(height: 16.h),

        if (homework.description != null) ...[
          Text(
            homework.description!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDark
                  ? AppColors.textSecondaryDark
                  : AppColors.textSecondaryLight,
            ),
          ),
          SizedBox(height: 16.h),
        ],

        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: isDark ? AppColors.borderDark : AppColors.borderLight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Info rows
              _InfoRow(
                icon: Symbols.school,
                label: 'Subject',
                value: homework.subject,
              ),

              SizedBox(height: 12.h),

              // Assignment type row
              _InfoRow(
                icon: homework.assignmentType.icon,
                label: 'Assignment Type',
                value: homework.assignmentType.label,
                valueColor: homework.assignmentType.color,
              ),

              SizedBox(height: 12.h),

              // Class information (for class assignments)
              if (homework.assignmentType == AssignmentType.classAssignment &&
                  homework.classId != null)
                ..._buildClassInfoRow(ref, homework.classId!),

              // Individual/Group assignment info
              if (homework.assignmentType != AssignmentType.classAssignment &&
                  homework.assignedUserIds.isNotEmpty)
                ..._buildAssignedUsersRow(homework),

              _InfoRow(
                icon: Symbols.schedule,
                label: 'Assigned',
                value: _formatDate(homework.assignedAt),
              ),

              SizedBox(height: 12.h),

              _InfoRow(
                icon: Symbols.event,
                label: 'Due Date',
                value: _getDueDateText(homework.dueAt, homework.status),
                valueColor: _getDueDateColor(homework.dueAt, isDark),
              ),

              SizedBox(height: 12.h),

              _InfoRow(
                icon: homework.requiresSubmission
                    ? Symbols.upload
                    : Symbols.check_circle,
                label: 'Submission',
                value: homework.requiresSubmission
                    ? 'Required'
                    : 'Not required',
              ),

              if (homework.requiresSubmission) ...[
                SizedBox(height: 12.h),
                _InfoRow(
                  icon: homework.submissionType == SubmissionType.online
                      ? Symbols.cloud_upload
                      : Symbols.assignment_turned_in,
                  label: 'Type',
                  value: homework.submissionType.label,
                ),
              ],

              SizedBox(height: 12.h),

              _InfoRow(
                icon: Symbols.flag,
                label: 'Status',
                value: homework.status.label,
                valueColor: homework.status.color,
                showStatusChip: true,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build class information row for class assignments
  List<Widget> _buildClassInfoRow(WidgetRef ref, String classId) {
    final classAsync = ref.watch(classDetailProvider(classId));

    return [
      classAsync.when(
        loading: () =>
            _InfoRow(icon: Symbols.class_, label: 'Class', value: 'Loading...'),
        error: (_, __) => _InfoRow(
          icon: Symbols.class_,
          label: 'Class',
          value: 'Unknown Class',
        ),
        data: (classModel) => _InfoRow(
          icon: Symbols.class_,
          label: 'Class',
          value: classModel?.name ?? 'Unknown Class',
        ),
      ),
      SizedBox(height: 12.h),
    ];
  }

  /// Build assigned users row for individual/group assignments
  List<Widget> _buildAssignedUsersRow(HomeworkModel homework) {
    final userCount = homework.assignedUserIds.length;
    String value;

    if (homework.assignmentType == AssignmentType.individual) {
      value = userCount == 1 ? 'You' : '$userCount students';
    } else if (homework.assignmentType == AssignmentType.group) {
      value = '$userCount group members';
    } else {
      value = '$userCount assigned users';
    }

    return [
      _InfoRow(
        icon: homework.assignmentType == AssignmentType.individual
            ? Symbols.person
            : Symbols.group,
        label: 'Assigned To',
        value: value,
      ),
      SizedBox(height: 12.h),
    ];
  }
}

/// Individual info row widget
class _InfoRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color? valueColor;
  final bool showStatusChip;

  const _InfoRow({
    required this.icon,
    required this.label,
    required this.value,
    this.valueColor,
    this.showStatusChip = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Row(
      children: [
        Icon(
          icon,
          size: 20.sp,
          color: isDark
              ? AppColors.textSecondaryDark
              : AppColors.textSecondaryLight,
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDark
                      ? AppColors.textSecondaryDark
                      : AppColors.textSecondaryLight,
                ),
              ),
              if (showStatusChip)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: (valueColor ?? theme.colorScheme.primary).withValues(
                      alpha: 0.1,
                    ),
                    borderRadius: BorderRadius.circular(6.r),
                    border: Border.all(
                      color: (valueColor ?? theme.colorScheme.primary)
                          .withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    value,
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: valueColor ?? theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                )
              else
                Flexible(
                  child: Text(
                    value,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color:
                          valueColor ??
                          (isDark
                              ? AppColors.textPrimaryDark
                              : AppColors.textPrimaryLight),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
