import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:scholara_student/core/providers/auth_providers.dart';
import 'package:scholara_student/core/routes/app_routes.dart';
import 'package:scholara_student/core/widgets/responsive/responsive_page.dart';
import 'package:scholara_student/core/widgets/theme/theme_mode_switcher.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return ResponsivePage(
      mobile: (context) => Scaffold(
        appBar: AppBar(
          title: Text(
            'Dashboard',
            style: textTheme.titleLarge?.copyWith(color: colorScheme.onSurface),
          ),
          backgroundColor: colorScheme.surface,
          elevation: 0,
          automaticallyImplyLeading: false, // Remove back button
          actions: [
            const ThemeModeSwitcher(),
            PopupMenuButton<String>(
              onSelected: (value) async {
                if (value == 'logout') {
                  await ref.read(authStateProvider.notifier).signOut();
                  if (context.mounted) {
                    context.goNamed(RouteNames.login);
                  }
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'logout',
                  child: Row(
                    children: [
                      Icon(Icons.logout),
                      SizedBox(width: 8),
                      Text('Logout'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            DashboardTile(
              title: 'Homework',
              icon: Icons.assignment,
              onTap: () {
                context.pushNamed(RouteNames.homeworkList);
              },
            ),
            const SizedBox(height: 12),
            DashboardTile(
              title: 'Classrooms',
              icon: Icons.assignment,
              onTap: () {
                context.pushNamed(RouteNames.classroomsList);
              },
            ),
            const SizedBox(height: 12),
            DashboardTile(
              title: 'Profile',
              icon: Icons.person,
              onTap: () {
                context.pushNamed(RouteNames.profile);
              },
            ),
            const SizedBox(height: 12),
            DashboardTile(title: 'Coming Soon', icon: Icons.lock, onTap: () {}),

            // Debug tiles (only show in debug mode)
            if (kDebugMode) ...[
              const SizedBox(height: 24),
              Text(
                'Debug Tools',
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              DashboardTile(
                title: 'Debug Info',
                icon: Icons.info_outline,
                onTap: () {
                  context.pushNamed(RouteNames.debugInfo);
                },
              ),
              const SizedBox(height: 12),
              DashboardTile(
                title: 'Debug Logs',
                icon: Icons.list_alt,
                onTap: () {
                  context.pushNamed(RouteNames.debugLogs);
                },
              ),
              const SizedBox(height: 12),
              DashboardTile(
                title: 'Mock Data Management',
                icon: Icons.data_object,
                onTap: () {
                  context.pushNamed(RouteNames.mockDataManagement);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class DashboardTile extends StatelessWidget {
  final String title;
  final IconData icon;
  final VoidCallback onTap;

  const DashboardTile({
    super.key,
    required this.title,
    required this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.03),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(icon, size: 28, color: colorScheme.primary),
            const SizedBox(width: 16),
            Text(
              title,
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios_rounded,
              size: 16,
              color: colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ],
        ),
      ),
    );
  }
}
