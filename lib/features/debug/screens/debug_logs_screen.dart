// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';

import '../../../core/services/debug_service.dart';
import '../../../core/widgets/responsive/responsive_page.dart';
import '../widgets/debug_log_tile.dart';
import '../widgets/debug_log_filter_chip.dart';

class DebugLogsScreen extends ConsumerStatefulWidget {
  const DebugLogsScreen({super.key});

  @override
  ConsumerState<DebugLogsScreen> createState() => _DebugLogsScreenState();
}

class _DebugLogsScreenState extends ConsumerState<DebugLogsScreen> {
  DebugLogType? _selectedFilter;
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final debugService = ref.watch(debugServiceProvider);

    final filteredLogs = _selectedFilter == null
        ? debugService.logs
        : debugService.getLogsByType(_selectedFilter!);

    return ResponsivePage(
      mobile: (context) => Scaffold(
        appBar: AppBar(
          title: Text(
            'Debug Logs',
            style: textTheme.titleLarge?.copyWith(color: colorScheme.onSurface),
          ),
          backgroundColor: colorScheme.surface,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.pop(),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: _exportLogs,
              tooltip: 'Export Logs',
            ),
            IconButton(
              icon: const Icon(Icons.clear_all),
              onPressed: _clearLogs,
              tooltip: 'Clear Logs',
            ),
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'add_test_logs':
                    _addTestLogs();
                    break;
                  case 'scroll_to_top':
                    _scrollToTop();
                    break;
                  case 'scroll_to_bottom':
                    _scrollToBottom();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'add_test_logs',
                  child: Row(
                    children: [
                      Icon(Icons.add),
                      SizedBox(width: 8),
                      Text('Add Test Logs'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'scroll_to_top',
                  child: Row(
                    children: [
                      Icon(Icons.keyboard_arrow_up),
                      SizedBox(width: 8),
                      Text('Scroll to Top'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'scroll_to_bottom',
                  child: Row(
                    children: [
                      Icon(Icons.keyboard_arrow_down),
                      SizedBox(width: 8),
                      Text('Scroll to Bottom'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: Column(
          children: [
            // Filter chips
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    DebugLogFilterChip(
                      label: 'All',
                      isSelected: _selectedFilter == null,
                      onSelected: () => setState(() => _selectedFilter = null),
                      count: debugService.logs.length,
                    ),
                    const SizedBox(width: 8),
                    ...DebugLogType.values.map(
                      (type) => Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: DebugLogFilterChip(
                          label: _formatLogType(type),
                          isSelected: _selectedFilter == type,
                          onSelected: () =>
                              setState(() => _selectedFilter = type),
                          count: debugService.getLogsByType(type).length,
                          color: _getLogTypeColor(type),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Divider(height: 1),

            // Logs list
            Expanded(
              child: filteredLogs.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.list_alt,
                            size: 64,
                            color: colorScheme.onSurface.withValues(alpha: 0.3),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _selectedFilter == null
                                ? 'No logs available'
                                : 'No ${_formatLogType(_selectedFilter!).toLowerCase()} logs',
                            style: textTheme.titleMedium?.copyWith(
                              color: colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Logs will appear here as the app runs',
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface.withValues(
                                alpha: 0.4,
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _addTestLogs,
                            child: const Text('Add Test Logs'),
                          ),
                        ],
                      ),
                    )
                  : ListView.separated(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: filteredLogs.length,
                      separatorBuilder: (context, index) =>
                          const SizedBox(height: 8),
                      itemBuilder: (context, index) {
                        final log = filteredLogs[index];
                        return DebugLogTile(log: log);
                      },
                    ),
            ),
          ],
        ),
        floatingActionButton: filteredLogs.isNotEmpty
            ? FloatingActionButton(
                onPressed: _scrollToTop,
                tooltip: 'Scroll to Top',
                child: const Icon(Icons.keyboard_arrow_up),
              )
            : null,
      ),
    );
  }

  String _formatLogType(DebugLogType type) {
    switch (type) {
      case DebugLogType.debug:
        return 'Debug';
      case DebugLogType.info:
        return 'Info';
      case DebugLogType.warning:
        return 'Warning';
      case DebugLogType.error:
        return 'Error';
      case DebugLogType.network:
        return 'Network';
      case DebugLogType.firebase:
        return 'Firebase';
      case DebugLogType.auth:
        return 'Auth';
      case DebugLogType.navigation:
        return 'Navigation';
    }
  }

  Color _getLogTypeColor(DebugLogType type) {
    switch (type) {
      case DebugLogType.debug:
        return Colors.grey;
      case DebugLogType.info:
        return Colors.blue;
      case DebugLogType.warning:
        return Colors.orange;
      case DebugLogType.error:
        return Colors.red;
      case DebugLogType.network:
        return Colors.purple;
      case DebugLogType.firebase:
        return Colors.amber;
      case DebugLogType.auth:
        return Colors.green;
      case DebugLogType.navigation:
        return Colors.teal;
    }
  }

  void _exportLogs() async {
    final debugService = ref.read(debugServiceProvider);
    final logsJson = debugService.exportLogs();

    await Share.share(logsJson);
  }

  void _clearLogs() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Logs'),
        content: const Text(
          'Are you sure you want to clear all logs? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(debugServiceProvider).clearLogs();
              Navigator.of(context).pop();
              setState(() {});
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _addTestLogs() {
    final debugService = ref.read(debugServiceProvider);

    debugService.logInfo('Test info message', source: 'DebugLogsScreen');
    debugService.logDebug('Test debug message', source: 'DebugLogsScreen');
    debugService.logWarning('Test warning message', source: 'DebugLogsScreen');
    debugService.logError('Test error message', source: 'DebugLogsScreen');
    debugService.logNetwork(
      'Test network request',
      source: 'DebugLogsScreen',
      metadata: {
        'url': 'https://api.example.com/test',
        'method': 'GET',
        'status': 200,
      },
    );
    debugService.logFirebase(
      'Test Firebase operation',
      source: 'DebugLogsScreen',
    );
    debugService.logAuth('Test auth operation', source: 'DebugLogsScreen');
    debugService.logNavigation(
      'Test navigation',
      source: 'DebugLogsScreen',
      metadata: {'from': '/debug', 'to': '/home'},
    );

    setState(() {});
  }

  void _scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
}
