import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/services/debug_service.dart';
import '../../../core/widgets/responsive/responsive_page.dart';
import '../widgets/debug_info_section.dart';

class DebugInfoScreen extends ConsumerStatefulWidget {
  const DebugInfoScreen({super.key});

  @override
  ConsumerState<DebugInfoScreen> createState() => _DebugInfoScreenState();
}

class _DebugInfoScreenState extends ConsumerState<DebugInfoScreen> {
  bool _isLoading = true;
  Map<String, dynamic> _debugData = {};

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    setState(() => _isLoading = true);

    try {
      final debugService = ref.read(debugServiceProvider);

      final deviceInfo = await debugService.getDeviceInfo();
      final connectivityInfo = await debugService.getConnectivityInfo();
      final firestoreStatus = await debugService.getFirestoreStatus();
      final localStorageInfo = await debugService.getLocalStorageInfo();
      final firebaseInfo = debugService.getFirebaseInfo();
      final firebaseAuthStatus = debugService.getFirebaseAuthStatus();
      final providerStates = debugService.getProviderStates(ref);

      // Get app state info safely
      Map<String, dynamic> appStateInfo = {};
      if (mounted) {
        appStateInfo = debugService.getAppStateInfo(context);
      }

      setState(() {
        _debugData = {
          'deviceInfo': deviceInfo,
          'connectivityInfo': connectivityInfo,
          'firestoreStatus': firestoreStatus,
          'localStorageInfo': localStorageInfo,
          'firebaseInfo': firebaseInfo,
          'firebaseAuthStatus': firebaseAuthStatus,
          'providerStates': providerStates,
          'appStateInfo': appStateInfo,
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _debugData = {'error': e.toString()};
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return ResponsivePage(
      mobile: (context) => Scaffold(
        appBar: AppBar(
          title: Text(
            'Debug Info',
            style: textTheme.titleLarge?.copyWith(color: colorScheme.onSurface),
          ),
          backgroundColor: colorScheme.surface,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.pop(),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadDebugInfo,
              tooltip: 'Refresh Debug Info',
            ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _debugData.containsKey('error')
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading debug info',
                      style: textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _debugData['error'],
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.error,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadDebugInfo,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
            : RefreshIndicator(
                onRefresh: _loadDebugInfo,
                child: ListView(
                  padding: const EdgeInsets.all(16),
                  children: [
                    // User & Auth Information
                    DebugInfoSection(
                      title: 'User & Authentication',
                      icon: Icons.person,
                      data: _debugData['firebaseAuthStatus'] ?? {},
                    ),
                    const SizedBox(height: 16),

                    // Provider States
                    DebugInfoSection(
                      title: 'Provider States',
                      icon: Icons.settings,
                      data: _debugData['providerStates'] ?? {},
                    ),
                    const SizedBox(height: 16),

                    // App State
                    DebugInfoSection(
                      title: 'App State',
                      icon: Icons.apps,
                      data: _debugData['appStateInfo'] ?? {},
                    ),
                    const SizedBox(height: 16),

                    // Device Information
                    DebugInfoSection(
                      title: 'Device Information',
                      icon: Icons.phone_android,
                      data: _debugData['deviceInfo'] ?? {},
                    ),
                    const SizedBox(height: 16),

                    // Firebase Configuration
                    DebugInfoSection(
                      title: 'Firebase Configuration',
                      icon: Icons.cloud,
                      data: _debugData['firebaseInfo'] ?? {},
                    ),
                    const SizedBox(height: 16),

                    // Firestore Status
                    DebugInfoSection(
                      title: 'Firestore Status',
                      icon: Icons.storage,
                      data: _debugData['firestoreStatus'] ?? {},
                    ),
                    const SizedBox(height: 16),

                    // Network Connectivity
                    DebugInfoSection(
                      title: 'Network Connectivity',
                      icon: Icons.wifi,
                      data: _debugData['connectivityInfo'] ?? {},
                    ),
                    const SizedBox(height: 16),

                    // Local Storage
                    DebugInfoSection(
                      title: 'Local Storage',
                      icon: Icons.save,
                      data: _debugData['localStorageInfo'] ?? {},
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
      ),
    );
  }
}
