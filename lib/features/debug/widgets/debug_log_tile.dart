import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/services/debug_service.dart';

class DebugLogTile extends StatefulWidget {
  final DebugLogEntry log;

  const DebugLogTile({
    super.key,
    required this.log,
  });

  @override
  State<DebugLogTile> createState() => _DebugLogTileState();
}

class _DebugLogTileState extends State<DebugLogTile> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getLogTypeColor(widget.log.type).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () => setState(() => _isExpanded = !_isExpanded),
            onLongPress: () => _copyToClipboard(widget.log.message),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header row
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getLogTypeColor(widget.log.type),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _formatLogType(widget.log.type),
                          style: textTheme.labelSmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (widget.log.source != null) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            widget.log.source!,
                            style: textTheme.labelSmall?.copyWith(
                              color: colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      Expanded(
                        child: Text(
                          _formatTimestamp(widget.log.timestamp),
                          style: textTheme.labelSmall?.copyWith(
                            color: colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                          textAlign: TextAlign.end,
                        ),
                      ),
                      if (widget.log.metadata != null) ...[
                        const SizedBox(width: 8),
                        Icon(
                          _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                          size: 16,
                          color: colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 8),
                  
                  // Message
                  Text(
                    widget.log.message,
                    style: textTheme.bodyMedium?.copyWith(
                      color: _getMessageColor(widget.log.type, colorScheme),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Expanded metadata section
          if (_isExpanded && widget.log.metadata != null) ...[
            const Divider(height: 1),
            Container(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Metadata',
                    style: textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ..._buildMetadataItems(widget.log.metadata!, textTheme, colorScheme),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  List<Widget> _buildMetadataItems(
    Map<String, dynamic> metadata,
    TextTheme textTheme,
    ColorScheme colorScheme,
  ) {
    final items = <Widget>[];

    metadata.forEach((key, value) {
      items.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  key,
                  style: textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 3,
                child: GestureDetector(
                  onLongPress: () => _copyToClipboard(value.toString()),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      value.toString(),
                      style: textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                        color: colorScheme.onSurface.withValues(alpha: 0.8),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });

    return items;
  }

  String _formatLogType(DebugLogType type) {
    switch (type) {
      case DebugLogType.debug:
        return 'DEBUG';
      case DebugLogType.info:
        return 'INFO';
      case DebugLogType.warning:
        return 'WARN';
      case DebugLogType.error:
        return 'ERROR';
      case DebugLogType.network:
        return 'NET';
      case DebugLogType.firebase:
        return 'FB';
      case DebugLogType.auth:
        return 'AUTH';
      case DebugLogType.navigation:
        return 'NAV';
    }
  }

  Color _getLogTypeColor(DebugLogType type) {
    switch (type) {
      case DebugLogType.debug:
        return Colors.grey;
      case DebugLogType.info:
        return Colors.blue;
      case DebugLogType.warning:
        return Colors.orange;
      case DebugLogType.error:
        return Colors.red;
      case DebugLogType.network:
        return Colors.purple;
      case DebugLogType.firebase:
        return Colors.amber;
      case DebugLogType.auth:
        return Colors.green;
      case DebugLogType.navigation:
        return Colors.teal;
    }
  }

  Color _getMessageColor(DebugLogType type, ColorScheme colorScheme) {
    switch (type) {
      case DebugLogType.error:
        return Colors.red.shade700;
      case DebugLogType.warning:
        return Colors.orange.shade700;
      default:
        return colorScheme.onSurface;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return '${difference.inSeconds}s ago';
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Copied to clipboard'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
