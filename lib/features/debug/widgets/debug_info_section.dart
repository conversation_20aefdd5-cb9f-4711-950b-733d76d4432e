import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class DebugInfoSection extends StatefulWidget {
  final String title;
  final IconData icon;
  final Map<String, dynamic> data;

  const DebugInfoSection({
    super.key,
    required this.title,
    required this.icon,
    required this.data,
  });

  @override
  State<DebugInfoSection> createState() => _DebugInfoSectionState();
}

class _DebugInfoSectionState extends State<DebugInfoSection> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () => setState(() => _isExpanded = !_isExpanded),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    widget.icon,
                    color: colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.title,
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Icon(
                    _isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ],
              ),
            ),
          ),
          if (_isExpanded) ...[
            const Divider(height: 1),
            Container(
              padding: const EdgeInsets.all(16),
              child: widget.data.isEmpty
                  ? Text(
                      'No data available',
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.6),
                        fontStyle: FontStyle.italic,
                      ),
                    )
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: _buildDataItems(widget.data, textTheme, colorScheme),
                    ),
            ),
          ],
        ],
      ),
    );
  }

  List<Widget> _buildDataItems(
    Map<String, dynamic> data,
    TextTheme textTheme,
    ColorScheme colorScheme,
  ) {
    final items = <Widget>[];

    data.forEach((key, value) {
      items.add(_buildDataItem(key, value, textTheme, colorScheme));
      if (items.length < data.length * 2 - 1) {
        items.add(const SizedBox(height: 8));
      }
    });

    return items;
  }

  Widget _buildDataItem(
    String key,
    dynamic value,
    TextTheme textTheme,
    ColorScheme colorScheme,
  ) {
    if (value is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            key,
            style: textTheme.labelLarge?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            margin: const EdgeInsets.only(left: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: _buildDataItems(value, textTheme, colorScheme),
            ),
          ),
        ],
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            _formatKey(key),
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          flex: 3,
          child: GestureDetector(
            onLongPress: () => _copyToClipboard(value.toString()),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                _formatValue(value),
                style: textTheme.bodySmall?.copyWith(
                  fontFamily: 'monospace',
                  color: _getValueColor(value, colorScheme),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _formatKey(String key) {
    // Convert camelCase to Title Case
    return key
        .replaceAllMapped(RegExp(r'([A-Z])'), (match) => ' ${match.group(1)}')
        .split(' ')
        .map((word) => word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1))
        .join(' ')
        .trim();
  }

  String _formatValue(dynamic value) {
    if (value == null) return 'null';
    if (value is bool) return value.toString();
    if (value is String) {
      if (value.isEmpty) return '(empty)';
      if (value.length > 50) return '${value.substring(0, 47)}...';
      return value;
    }
    if (value is num) return value.toString();
    if (value is DateTime) return value.toIso8601String();
    return value.toString();
  }

  Color _getValueColor(dynamic value, ColorScheme colorScheme) {
    if (value == null) {
      return colorScheme.onSurface.withValues(alpha: 0.4);
    }
    if (value is bool) {
      return value ? Colors.green : Colors.red;
    }
    if (value is String) {
      if (value.toLowerCase().contains('error') || value.toLowerCase().contains('failed')) {
        return Colors.red;
      }
      if (value.toLowerCase().contains('success') || value.toLowerCase().contains('connected')) {
        return Colors.green;
      }
    }
    return colorScheme.onSurface.withValues(alpha: 0.8);
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Copied to clipboard'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
