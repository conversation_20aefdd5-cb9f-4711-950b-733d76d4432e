import '../enums/auth_enums.dart';

/// Model representing a user in the Scholara student app
class UserModel {
  /// Unique identifier for the user (Firebase UID)
  final String id;

  /// User's email address
  final String email;

  /// User's display name
  final String? displayName;

  /// User's profile photo URL
  final String? photoUrl;

  /// Whether the user's email is verified
  final bool emailVerified;

  /// When the user account was created
  final DateTime? createdAt;

  /// When the user last signed in
  final DateTime? lastSignInAt;

  /// User's phone number (optional)
  final String? phoneNumber;

  /// Additional user metadata
  final Map<String, dynamic>? metadata;

  // Profile-specific fields

  /// User's full name (separate from displayName for profile purposes)
  final String? fullName;

  /// Type of user (student, parent, teacher, admin, other)
  final UserType userType;

  /// ID of the user's primary classroom (determines grade, section, etc.)
  /// Must be a classroom where isPrimaryClass is true
  final String? primaryClassId;

  /// Profile image URL (separate from photoUrl for profile-specific images)
  final String? profileImageUrl;

  /// User's grade level (for students)
  final String? grade;

  /// User's school name
  final String? school;

  /// Student ID (for students)
  final String? studentId;

  /// User's bio or description
  final String? bio;

  /// List of subjects the user is associated with
  final List<String> subjects;

  const UserModel({
    required this.id,
    required this.email,
    this.displayName,
    this.photoUrl,
    required this.emailVerified,
    this.createdAt,
    this.lastSignInAt,
    this.phoneNumber,
    this.metadata,
    // Profile-specific fields with defaults
    this.fullName,
    this.userType = UserType.student, // Default to student
    this.primaryClassId,
    this.profileImageUrl,
    this.grade,
    this.school,
    this.studentId,
    this.bio,
    this.subjects = const [], // Default to empty list
  });

  /// Create a UserModel from Firebase User
  factory UserModel.fromFirebaseUser(dynamic firebaseUser) {
    return UserModel(
      id: firebaseUser.uid,
      email: firebaseUser.email ?? '',
      displayName: firebaseUser.displayName,
      photoUrl: firebaseUser.photoURL,
      emailVerified: firebaseUser.emailVerified ?? false,
      createdAt: firebaseUser.metadata?.creationTime,
      lastSignInAt: firebaseUser.metadata?.lastSignInTime,
      phoneNumber: firebaseUser.phoneNumber,
      // Profile fields will use defaults since Firebase User doesn't have them
      fullName: firebaseUser.displayName, // Use displayName as fallback
      userType: UserType.student, // Default to student
      primaryClassId: null, // No primary class by default
      profileImageUrl: firebaseUser.photoURL, // Use photoURL as fallback
      grade: null, // Will be set later from profile data
      school: null, // Will be set later from profile data
      studentId: null, // Will be set later from profile data
      bio: null, // Will be set later from profile data
      subjects: const [], // Empty by default
    );
  }

  /// Create a UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      displayName: json['displayName'] as String?,
      photoUrl: json['photoUrl'] as String?,
      emailVerified: json['emailVerified'] as bool? ?? false,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      lastSignInAt: json['lastSignInAt'] != null
          ? DateTime.parse(json['lastSignInAt'] as String)
          : null,
      phoneNumber: json['phoneNumber'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      // Profile fields with safe parsing
      fullName: json['fullName'] as String?,
      userType: json['userType'] != null
          ? UserTypeExtension.fromString(json['userType'] as String)
          : UserType.student,
      primaryClassId: json['primaryClassId'] as String?,
      profileImageUrl: json['profileImageUrl'] as String?,
      grade: json['grade'] as String?,
      school: json['school'] as String?,
      studentId: json['studentId'] as String?,
      bio: json['bio'] as String?,
      subjects: json['subjects'] != null
          ? List<String>.from(json['subjects'] as List)
          : const [],
    );
  }

  /// Convert UserModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'photoUrl': photoUrl,
      'emailVerified': emailVerified,
      'createdAt': createdAt?.toIso8601String(),
      'lastSignInAt': lastSignInAt?.toIso8601String(),
      'phoneNumber': phoneNumber,
      'metadata': metadata,
      // Profile fields
      'fullName': fullName,
      'userType': userType.value,
      'primaryClassId': primaryClassId,
      'profileImageUrl': profileImageUrl,
      'grade': grade,
      'school': school,
      'studentId': studentId,
      'bio': bio,
      'subjects': subjects,
    };
  }

  /// Create a copy of this UserModel with updated fields
  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoUrl,
    bool? emailVerified,
    DateTime? createdAt,
    DateTime? lastSignInAt,
    String? phoneNumber,
    Map<String, dynamic>? metadata,
    // Profile fields
    String? fullName,
    UserType? userType,
    String? primaryClassId,
    String? profileImageUrl,
    String? grade,
    String? school,
    String? studentId,
    String? bio,
    List<String>? subjects,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      emailVerified: emailVerified ?? this.emailVerified,
      createdAt: createdAt ?? this.createdAt,
      lastSignInAt: lastSignInAt ?? this.lastSignInAt,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      metadata: metadata ?? this.metadata,
      // Profile fields
      fullName: fullName ?? this.fullName,
      userType: userType ?? this.userType,
      primaryClassId: primaryClassId ?? this.primaryClassId,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      grade: grade ?? this.grade,
      school: school ?? this.school,
      studentId: studentId ?? this.studentId,
      bio: bio ?? this.bio,
      subjects: subjects ?? this.subjects,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel &&
        other.id == id &&
        other.email == email &&
        other.displayName == displayName &&
        other.photoUrl == photoUrl &&
        other.emailVerified == emailVerified &&
        other.createdAt == createdAt &&
        other.lastSignInAt == lastSignInAt &&
        other.phoneNumber == phoneNumber &&
        other.fullName == fullName &&
        other.userType == userType &&
        other.primaryClassId == primaryClassId &&
        other.profileImageUrl == profileImageUrl &&
        other.grade == grade &&
        other.school == school &&
        other.studentId == studentId &&
        other.bio == bio &&
        other.subjects == subjects;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      email,
      displayName,
      photoUrl,
      emailVerified,
      createdAt,
      lastSignInAt,
      phoneNumber,
      fullName,
      userType,
      primaryClassId,
      profileImageUrl,
      grade,
      school,
      studentId,
      bio,
      subjects,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, displayName: $displayName, fullName: $fullName, userType: ${userType.displayName}, emailVerified: $emailVerified)';
  }
}
