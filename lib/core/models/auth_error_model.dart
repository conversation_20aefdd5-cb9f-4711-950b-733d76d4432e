import '../enums/auth_enums.dart';

/// Model representing an authentication error
class AuthErrorModel {
  /// The type of authentication error
  final AuthErrorType type;
  
  /// The raw error code from Firebase
  final String? code;
  
  /// The raw error message from Firebase
  final String? rawMessage;
  
  /// User-friendly error message
  final String message;
  
  /// The operation that caused the error
  final AuthOperation? operation;
  
  /// Additional error details
  final Map<String, dynamic>? details;
  
  /// When the error occurred
  final DateTime timestamp;

  const AuthErrorModel({
    required this.type,
    this.code,
    this.rawMessage,
    required this.message,
    this.operation,
    this.details,
    required this.timestamp,
  });

  /// Create an AuthErrorModel from a Firebase Auth exception
  factory AuthErrorModel.fromFirebaseException(
    dynamic exception, {
    AuthOperation? operation,
  }) {
    final code = exception.code as String?;
    final message = exception.message as String?;
    
    final errorType = _mapFirebaseCodeToErrorType(code);
    
    return AuthErrorModel(
      type: errorType,
      code: code,
      rawMessage: message,
      message: errorType.message,
      operation: operation,
      timestamp: DateTime.now(),
      details: {
        'firebaseCode': code,
        'firebaseMessage': message,
      },
    );
  }

  /// Create an AuthErrorModel from a generic exception
  factory AuthErrorModel.fromException(
    Exception exception, {
    AuthOperation? operation,
  }) {
    return AuthErrorModel(
      type: AuthErrorType.unknown,
      rawMessage: exception.toString(),
      message: AuthErrorType.unknown.message,
      operation: operation,
      timestamp: DateTime.now(),
      details: {
        'exceptionType': exception.runtimeType.toString(),
        'exceptionMessage': exception.toString(),
      },
    );
  }

  /// Create a custom AuthErrorModel
  factory AuthErrorModel.custom({
    required AuthErrorType type,
    String? customMessage,
    AuthOperation? operation,
    Map<String, dynamic>? details,
  }) {
    return AuthErrorModel(
      type: type,
      message: customMessage ?? type.message,
      operation: operation,
      details: details,
      timestamp: DateTime.now(),
    );
  }

  /// Map Firebase error codes to AuthErrorType
  static AuthErrorType _mapFirebaseCodeToErrorType(String? code) {
    switch (code) {
      case 'invalid-email':
        return AuthErrorType.invalidEmail;
      case 'weak-password':
        return AuthErrorType.weakPassword;
      case 'email-already-in-use':
        return AuthErrorType.emailAlreadyInUse;
      case 'user-not-found':
        return AuthErrorType.userNotFound;
      case 'wrong-password':
        return AuthErrorType.wrongPassword;
      case 'user-disabled':
        return AuthErrorType.userDisabled;
      case 'too-many-requests':
        return AuthErrorType.tooManyRequests;
      case 'network-request-failed':
        return AuthErrorType.networkError;
      case 'operation-not-allowed':
        return AuthErrorType.operationNotAllowed;
      case 'invalid-credential':
        return AuthErrorType.invalidCredential;
      case 'account-exists-with-different-credential':
        return AuthErrorType.accountExistsWithDifferentCredential;
      case 'requires-recent-login':
        return AuthErrorType.requiresRecentLogin;
      case 'provider-already-linked':
        return AuthErrorType.providerAlreadyLinked;
      case 'no-such-provider':
        return AuthErrorType.noSuchProvider;
      case 'invalid-user-token':
        return AuthErrorType.invalidUserToken;
      case 'user-token-expired':
        return AuthErrorType.userTokenExpired;
      case 'app-not-authorized':
        return AuthErrorType.appNotAuthorized;
      case 'expired-action-code':
        return AuthErrorType.expiredActionCode;
      case 'invalid-action-code':
        return AuthErrorType.invalidActionCode;
      case 'invalid-message-payload':
        return AuthErrorType.invalidMessagePayload;
      case 'invalid-sender':
        return AuthErrorType.invalidSender;
      case 'invalid-recipient-email':
        return AuthErrorType.invalidRecipientEmail;
      case 'missing-android-pkg-name':
        return AuthErrorType.missingAndroidPackageName;
      case 'missing-continue-uri':
        return AuthErrorType.missingContinueUri;
      case 'missing-ios-bundle-id':
        return AuthErrorType.missingIosBundleId;
      case 'invalid-continue-uri':
        return AuthErrorType.invalidContinueUri;
      case 'unauthorized-continue-uri':
        return AuthErrorType.unauthorizedContinueUri;
      default:
        return AuthErrorType.unknown;
    }
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'code': code,
      'rawMessage': rawMessage,
      'message': message,
      'operation': operation?.name,
      'details': details,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create from JSON
  factory AuthErrorModel.fromJson(Map<String, dynamic> json) {
    return AuthErrorModel(
      type: AuthErrorType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => AuthErrorType.unknown,
      ),
      code: json['code'] as String?,
      rawMessage: json['rawMessage'] as String?,
      message: json['message'] as String,
      operation: json['operation'] != null
          ? AuthOperation.values.firstWhere(
              (e) => e.name == json['operation'],
              orElse: () => AuthOperation.signIn,
            )
          : null,
      details: json['details'] as Map<String, dynamic>?,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  @override
  String toString() {
    return 'AuthErrorModel(type: $type, code: $code, message: $message, operation: $operation)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthErrorModel &&
        other.type == type &&
        other.code == code &&
        other.message == message &&
        other.operation == operation;
  }

  @override
  int get hashCode {
    return Object.hash(type, code, message, operation);
  }
}
