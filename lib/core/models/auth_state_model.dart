import '../enums/auth_enums.dart';
import 'auth_error_model.dart';
import 'user_model.dart';

/// Model representing the complete authentication state
class AuthStateModel {
  /// Current authentication state
  final AuthState state;
  
  /// Current authenticated user (null if not authenticated)
  final UserModel? user;
  
  /// Current authentication error (null if no error)
  final AuthErrorModel? error;
  
  /// Whether an authentication operation is in progress
  final bool isLoading;
  
  /// The current operation being performed
  final AuthOperation? currentOperation;
  
  /// When the state was last updated
  final DateTime lastUpdated;

  const AuthStateModel({
    required this.state,
    this.user,
    this.error,
    this.isLoading = false,
    this.currentOperation,
    required this.lastUpdated,
  });

  /// Create an initial/unauthenticated state
  factory AuthStateModel.initial() {
    return AuthStateModel(
      state: AuthState.unauthenticated,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create a loading state
  factory AuthStateModel.loading({
    AuthOperation? operation,
    UserModel? currentUser,
  }) {
    return AuthStateModel(
      state: AuthState.loading,
      user: currentUser,
      isLoading: true,
      currentOperation: operation,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create an authenticated state
  factory AuthStateModel.authenticated(UserModel user) {
    return AuthStateModel(
      state: AuthState.authenticated,
      user: user,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create an unauthenticated state
  factory AuthStateModel.unauthenticated() {
    return AuthStateModel(
      state: AuthState.unauthenticated,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create an error state
  factory AuthStateModel.error(
    AuthErrorModel error, {
    UserModel? currentUser,
  }) {
    return AuthStateModel(
      state: AuthState.error,
      user: currentUser,
      error: error,
      lastUpdated: DateTime.now(),
    );
  }

  /// Check if user is authenticated
  bool get isAuthenticated => state == AuthState.authenticated && user != null;

  /// Check if there's an error
  bool get hasError => error != null;

  /// Check if the state is loading
  bool get isLoadingState => state == AuthState.loading || isLoading;

  /// Create a copy with updated fields
  AuthStateModel copyWith({
    AuthState? state,
    UserModel? user,
    AuthErrorModel? error,
    bool? isLoading,
    AuthOperation? currentOperation,
    DateTime? lastUpdated,
    bool clearUser = false,
    bool clearError = false,
  }) {
    return AuthStateModel(
      state: state ?? this.state,
      user: clearUser ? null : (user ?? this.user),
      error: clearError ? null : (error ?? this.error),
      isLoading: isLoading ?? this.isLoading,
      currentOperation: currentOperation ?? this.currentOperation,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'state': state.name,
      'user': user?.toJson(),
      'error': error?.toJson(),
      'isLoading': isLoading,
      'currentOperation': currentOperation?.name,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Create from JSON
  factory AuthStateModel.fromJson(Map<String, dynamic> json) {
    return AuthStateModel(
      state: AuthState.values.firstWhere(
        (e) => e.name == json['state'],
        orElse: () => AuthState.unauthenticated,
      ),
      user: json['user'] != null 
          ? UserModel.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      error: json['error'] != null
          ? AuthErrorModel.fromJson(json['error'] as Map<String, dynamic>)
          : null,
      isLoading: json['isLoading'] as bool? ?? false,
      currentOperation: json['currentOperation'] != null
          ? AuthOperation.values.firstWhere(
              (e) => e.name == json['currentOperation'],
              orElse: () => AuthOperation.signIn,
            )
          : null,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  @override
  String toString() {
    return 'AuthStateModel(state: $state, user: ${user?.email}, error: ${error?.type}, isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthStateModel &&
        other.state == state &&
        other.user == user &&
        other.error == error &&
        other.isLoading == isLoading &&
        other.currentOperation == currentOperation;
  }

  @override
  int get hashCode {
    return Object.hash(
      state,
      user,
      error,
      isLoading,
      currentOperation,
    );
  }
}
