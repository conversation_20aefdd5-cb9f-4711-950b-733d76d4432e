import 'package:flutter/material.dart';

class AppColors {
  // PRIMARY COLORS
  static const primaryLight = Color(0xFF009688);
  static const primaryDark = Color(0xFF26A69A);

  static const secondaryLight = Color(0xFF37474F);
  static const secondaryDark = Color(0xFF90A4AE);

  // BACKGROUND & SURFACE
  static const backgroundLight = Color(0xFFFAFAFA);
  static const backgroundDark = Color(0xFF121212);

  static const surfaceLight = Color(0xFFFFFFFF);
  static const surfaceDark = Color(0xFF1E1E1E);

  static const dividerLight = Color(0xFFE0E0E0);
  static const dividerDark = Color(0xFF424242);

  // STATUS COLORS
  static const successLight = Color(0xFF4CAF50);
  static const successDark = Color(0xFF66BB6A);

  static const warningLight = Color(0xFFFFC107);
  static const warningDark = Color(0xFFFFCA28);

  static const errorLight = Color(0xFFFF5252);
  static const errorDark = Color(0xFFFF6E6E);

  static const infoLight = Color(0xFF2196F3);
  static const infoDark = Color(0xFF42A5F5);

  // TEXT COLORS
  static const textPrimaryLight = Color(0xFF212121);
  static const textPrimaryDark = Color(0xFFFFFFFF);

  static const textSecondaryLight = Color(0xFF607D8B);
  static const textSecondaryDark = Color(0xFFB0BEC5);

  static const textErrorLight = Color(0xFFFF5252);
  static const textErrorDark = Color(0xFFFF6E6E);

  // BUTTON STATES
  static const buttonPrimaryLight = primaryLight;
  static const buttonPrimaryDark = primaryDark;

  static const buttonSecondaryLight = secondaryLight;
  static const buttonSecondaryDark = secondaryDark;

  static const buttonDisabledLight = Color(0xFFBDBDBD);
  static const buttonDisabledDark = Color(0xFF616161);

  // OUTLINES & BORDERS
  static const borderLight = Color(0xFFBDBDBD);
  static const borderDark = Color(0xFF616161);

  // TOOLTIP COLORS
  static const tooltipLight = infoLight;
  static const tooltipDark = infoDark;

  // SHADOWS & ELEVATION
  static const shadowLowLight = Color.fromRGBO(0, 0, 0, 0.05);
  static const shadowMediumLight = Color.fromRGBO(0, 0, 0, 0.10);
  static const shadowHighLight = Color.fromRGBO(0, 0, 0, 0.20);

  static const shadowLowDark = Color.fromRGBO(255, 255, 255, 0.05);
  static const shadowMediumDark = Color.fromRGBO(255, 255, 255, 0.10);
  static const shadowHighDark = Color.fromRGBO(255, 255, 255, 0.20);
}
