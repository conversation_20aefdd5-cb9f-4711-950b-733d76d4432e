import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Widget for displaying error messages with consistent styling
class ErrorDisplay extends StatelessWidget {
  /// Error message to display
  final String message;
  
  /// Optional error title
  final String? title;
  
  /// Optional retry callback
  final VoidCallback? onRetry;
  
  /// Optional dismiss callback
  final VoidCallback? onDismiss;
  
  /// Error display type
  final ErrorDisplayType type;
  
  /// Custom icon
  final Widget? icon;
  
  /// Whether to show the error in a card
  final bool showCard;

  const ErrorDisplay({
    super.key,
    required this.message,
    this.title,
    this.onRetry,
    this.onDismiss,
    this.type = ErrorDisplayType.inline,
    this.icon,
    this.showCard = true,
  });

  /// Create an inline error display
  const ErrorDisplay.inline({
    super.key,
    required this.message,
    this.title,
    this.onRetry,
    this.onDismiss,
    this.icon,
    this.showCard = true,
  }) : type = ErrorDisplayType.inline;

  /// Create a banner error display
  const ErrorDisplay.banner({
    super.key,
    required this.message,
    this.title,
    this.onRetry,
    this.onDismiss,
    this.icon,
  }) : type = ErrorDisplayType.banner,
       showCard = false;

  /// Create a full screen error display
  const ErrorDisplay.fullScreen({
    super.key,
    required this.message,
    this.title,
    this.onRetry,
    this.onDismiss,
    this.icon,
  }) : type = ErrorDisplayType.fullScreen,
       showCard = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    Widget content = _buildErrorContent(theme, colorScheme);

    switch (type) {
      case ErrorDisplayType.inline:
        if (showCard) {
          return Card(
            color: colorScheme.errorContainer,
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: content,
            ),
          );
        }
        return Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: colorScheme.errorContainer,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: colorScheme.error.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: content,
        );

      case ErrorDisplayType.banner:
        return Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          decoration: BoxDecoration(
            color: colorScheme.errorContainer,
            border: Border(
              bottom: BorderSide(
                color: colorScheme.error.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
          ),
          child: content,
        );

      case ErrorDisplayType.fullScreen:
        return Scaffold(
          backgroundColor: colorScheme.surface,
          body: Center(
            child: Padding(
              padding: EdgeInsets.all(24.w),
              child: content,
            ),
          ),
        );
    }
  }

  Widget _buildErrorContent(ThemeData theme, ColorScheme colorScheme) {
    return Column(
      mainAxisSize: type == ErrorDisplayType.fullScreen 
          ? MainAxisSize.min 
          : MainAxisSize.min,
      crossAxisAlignment: type == ErrorDisplayType.fullScreen 
          ? CrossAxisAlignment.center 
          : CrossAxisAlignment.start,
      children: [
        // Error icon and title row
        Row(
          children: [
            // Error icon
            icon ?? Icon(
              Symbols.error,
              color: colorScheme.error,
              size: type == ErrorDisplayType.fullScreen ? 32.w : 24.w,
            ),
            SizedBox(width: 12.w),
            
            // Title and dismiss button
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      title ?? 'Error',
                      style: (type == ErrorDisplayType.fullScreen 
                          ? theme.textTheme.headlineSmall 
                          : theme.textTheme.titleMedium)?.copyWith(
                        color: colorScheme.error,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  if (onDismiss != null)
                    IconButton(
                      onPressed: onDismiss,
                      icon: Icon(
                        Symbols.close,
                        color: colorScheme.error,
                        size: 20.w,
                      ),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(
                        minWidth: 24.w,
                        minHeight: 24.w,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
        
        // Error message
        SizedBox(height: 8.h),
        Text(
          message,
          style: (type == ErrorDisplayType.fullScreen 
              ? theme.textTheme.bodyLarge 
              : theme.textTheme.bodyMedium)?.copyWith(
            color: colorScheme.onErrorContainer,
          ),
          textAlign: type == ErrorDisplayType.fullScreen 
              ? TextAlign.center 
              : TextAlign.start,
        ),
        
        // Retry button
        if (onRetry != null) ...[
          SizedBox(height: 16.h),
          if (type == ErrorDisplayType.fullScreen)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: onRetry,
                icon: Icon(Symbols.refresh),
                label: Text('Try Again'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                ),
              ),
            )
          else
            Align(
              alignment: Alignment.centerRight,
              child: TextButton.icon(
                onPressed: onRetry,
                icon: Icon(
                  Symbols.refresh,
                  size: 16.w,
                ),
                label: Text('Retry'),
                style: TextButton.styleFrom(
                  foregroundColor: colorScheme.error,
                ),
              ),
            ),
        ],
      ],
    );
  }
}

/// Error display type enum
enum ErrorDisplayType {
  inline,
  banner,
  fullScreen,
}

/// Simple error message widget
class ErrorMessage extends StatelessWidget {
  /// Error message to display
  final String message;
  
  /// Text color
  final Color? color;
  
  /// Text style
  final TextStyle? style;

  const ErrorMessage({
    super.key,
    required this.message,
    this.color,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      children: [
        Icon(
          Symbols.error,
          color: color ?? colorScheme.error,
          size: 16.w,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            message,
            style: style ?? theme.textTheme.bodySmall?.copyWith(
              color: color ?? colorScheme.error,
            ),
          ),
        ),
      ],
    );
  }
}
