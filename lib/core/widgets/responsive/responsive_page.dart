import 'package:flutter/material.dart';

enum DeviceScreenType { mobile, tablet, desktop }

class ResponsivePage extends StatelessWidget {
  final WidgetBuilder mobile;
  final WidgetBuilder? tablet;
  final WidgetBuilder? desktop;

  const ResponsivePage({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  static DeviceScreenType getDeviceType(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    if (width >= 1000) return DeviceScreenType.desktop;
    if (width >= 600) return DeviceScreenType.tablet;
    return DeviceScreenType.mobile;
  }

  @override
  Widget build(BuildContext context) {
    final deviceType = getDeviceType(context);

    switch (deviceType) {
      case DeviceScreenType.desktop:
        if (desktop != null) return desktop!(context);
        if (tablet != null) return tablet!(context);
        return mobile(context);

      case DeviceScreenType.tablet:
        if (tablet != null) return tablet!(context);
        return mobile(context);

      case DeviceScreenType.mobile:
        return mobile(context);
    }
  }
}
