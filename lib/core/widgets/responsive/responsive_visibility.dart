import 'package:flutter/material.dart';

enum DeviceScreenType { mobile, tablet, desktop }

class ResponsiveVisibility extends StatelessWidget {
  final bool mobile;
  final bool? tablet;
  final bool? desktop;
  final Widget child;

  const ResponsiveVisibility({
    super.key,
    required this.child,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  static DeviceScreenType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width >= 1000) return DeviceScreenType.desktop;
    if (width >= 600) return DeviceScreenType.tablet;
    return DeviceScreenType.mobile;
  }

  @override
  Widget build(BuildContext context) {
    final deviceType = getDeviceType(context);

    final visible = switch (deviceType) {
      DeviceScreenType.desktop => desktop ?? tablet ?? mobile,
      DeviceScreenType.tablet => tablet ?? mobile,
      DeviceScreenType.mobile => mobile,
    };

    return visible ? child : const SizedBox.shrink();
  }
}
