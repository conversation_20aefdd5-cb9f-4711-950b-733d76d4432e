import 'package:flutter/material.dart';

enum DeviceScreenType { mobile, tablet, desktop }

class ResponsivePadding extends StatelessWidget {
  final EdgeInsets mobile;
  final EdgeInsets? tablet;
  final EdgeInsets? desktop;
  final Widget child;

  const ResponsivePadding({
    super.key,
    required this.child,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  static DeviceScreenType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width >= 1000) return DeviceScreenType.desktop;
    if (width >= 600) return DeviceScreenType.tablet;
    return DeviceScreenType.mobile;
  }

  @override
  Widget build(BuildContext context) {
    final deviceType = getDeviceType(context);

    final padding = switch (deviceType) {
      DeviceScreenType.desktop => desktop ?? tablet ?? mobile,
      DeviceScreenType.tablet => tablet ?? mobile,
      DeviceScreenType.mobile => mobile,
    };

    return Padding(
      padding: padding,
      child: child,
    );
  }
}
