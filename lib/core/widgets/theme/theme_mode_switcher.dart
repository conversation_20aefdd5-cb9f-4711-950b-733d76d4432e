import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:scholara_student/core/providers/user_preferences_provider.dart';

class ThemeModeSwitcher extends ConsumerWidget {
  const ThemeModeSwitcher({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mode = ref.watch(userPreferencesProvider);

    IconData icon;
    String label;

    switch (mode) {
      case ThemeMode.light:
        icon = Icons.light_mode;
        label = 'Light';
        break;
      case ThemeMode.dark:
        icon = Icons.dark_mode;
        label = 'Dark';
        break;
      default:
        icon = Icons.brightness_auto;
        label = 'System';
    }

    return IconButton(
      tooltip: 'Theme: $label',
      icon: Icon(icon),
      onPressed: () => ref.read(userPreferencesProvider.notifier).toggleTheme(),
    );
  }
}
