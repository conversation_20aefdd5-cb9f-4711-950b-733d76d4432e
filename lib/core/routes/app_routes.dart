class AppRoutes {
  // Authentication routes
  static const String login = '/login';
  static const String signUp = '/signup';
  static const String forgotPassword = '/forgot-password';

  // Main app routes
  static const String home = '/';
  static const String homeworkList = '/homework';
  static const String homeworkDetail = '/homework/:id';
  static const String submitHomework = '/homework/:id/submit';
  static const String viewSubmission = '/homework/:id/submission';

  // Classroom routes
  static const String classroomsList = '/classrooms';
  static const String classroomDetail = '/classrooms/:id';
  static const String activityFeed = '/classrooms/:id/activity';
  static const String classroomResources = '/classrooms/:id/resources';
  static const String classroomDiscussion = '/classrooms/:id/discussion';

  // Profile routes
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';

  // Debug routes
  static const String debugInfo = '/debug/info';
  static const String debugLogs = '/debug/logs';
  static const String mockDataManagement = '/debug/mock-data';
}

class RouteNames {
  // Authentication route names
  static const String login = 'login';
  static const String signUp = 'signUp';
  static const String forgotPassword = 'forgotPassword';

  // Main app route names
  static const String home = 'home';
  static const String homeworkList = 'homeworkList';
  static const String homeworkDetail = 'homeworkDetail';
  static const String submitHomework = 'submitHomework';
  static const String viewSubmission = 'viewSubmission';

  // Classroom route names
  static const String classroomsList = 'classroomsList';
  static const String classroomDetail = 'classroomDetail';
  static const String activityFeed = 'activityFeed';
  static const String classroomResources = 'classroomResources';
  static const String classroomDiscussion = 'classroomDiscussion';

  // Profile route names
  static const String profile = 'profile';
  static const String editProfile = 'editProfile';

  // Debug route names
  static const String debugInfo = 'debugInfo';
  static const String debugLogs = 'debugLogs';
  static const String mockDataManagement = 'mockDataManagement';
}
