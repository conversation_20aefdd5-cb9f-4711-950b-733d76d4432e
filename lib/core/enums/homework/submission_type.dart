enum SubmissionType { online, offline }

/// Extension to provide additional functionality for SubmissionType
extension SubmissionTypeExtension on SubmissionType {
  /// Returns a human-readable label for the submission type
  String get label {
    switch (this) {
      case SubmissionType.online:
        return 'Online';
      case SubmissionType.offline:
        return 'Offline';
    }
  }

  /// Returns whether this submission type requires file upload
  bool get requiresFileUpload {
    switch (this) {
      case SubmissionType.online:
        return true;
      case SubmissionType.offline:
        return false;
    }
  }
}
