import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/user_model.dart';
import '../models/auth_state_model.dart';

/// Service for managing local storage of user data and app preferences
class LocalStorageService {
  static final LocalStorageService _instance = LocalStorageService._internal();
  factory LocalStorageService() => _instance;
  LocalStorageService._internal();

  final Logger _logger = Logger();
  
  // Secure storage for sensitive data
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Keys for secure storage
  static const String _userDataKey = 'user_data';
  static const String _authStateKey = 'auth_state';
  static const String _userTokenKey = 'user_token';

  // Keys for shared preferences
  static const String _isFirstLaunchKey = 'is_first_launch';
  static const String _lastLoginTimeKey = 'last_login_time';
  static const String _appVersionKey = 'app_version';
  static const String _userPreferencesKey = 'user_preferences';

  SharedPreferences? _prefs;

  /// Initialize the service
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _logger.i('Local storage service initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize local storage service: $e');
      rethrow;
    }
  }

  /// Save user data securely
  Future<void> saveUserData(UserModel user) async {
    try {
      final userJson = jsonEncode(user.toJson());
      await _secureStorage.write(key: _userDataKey, value: userJson);
      _logger.i('User data saved successfully for user: ${user.email}');
    } catch (e) {
      _logger.e('Failed to save user data: $e');
      throw Exception('Failed to save user data locally');
    }
  }

  /// Get saved user data
  Future<UserModel?> getUserData() async {
    try {
      final userJson = await _secureStorage.read(key: _userDataKey);
      if (userJson == null) {
        _logger.i('No user data found in local storage');
        return null;
      }

      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      final user = UserModel.fromJson(userMap);
      _logger.i('User data retrieved successfully for user: ${user.email}');
      return user;
    } catch (e) {
      _logger.e('Failed to retrieve user data: $e');
      return null;
    }
  }

  /// Clear user data
  Future<void> clearUserData() async {
    try {
      await _secureStorage.delete(key: _userDataKey);
      _logger.i('User data cleared successfully');
    } catch (e) {
      _logger.e('Failed to clear user data: $e');
    }
  }

  /// Save authentication state
  Future<void> saveAuthState(AuthStateModel authState) async {
    try {
      final authStateJson = jsonEncode(authState.toJson());
      await _secureStorage.write(key: _authStateKey, value: authStateJson);
      _logger.i('Auth state saved successfully');
    } catch (e) {
      _logger.e('Failed to save auth state: $e');
    }
  }

  /// Get saved authentication state
  Future<AuthStateModel?> getAuthState() async {
    try {
      final authStateJson = await _secureStorage.read(key: _authStateKey);
      if (authStateJson == null) {
        _logger.i('No auth state found in local storage');
        return null;
      }

      final authStateMap = jsonDecode(authStateJson) as Map<String, dynamic>;
      final authState = AuthStateModel.fromJson(authStateMap);
      _logger.i('Auth state retrieved successfully');
      return authState;
    } catch (e) {
      _logger.e('Failed to retrieve auth state: $e');
      return null;
    }
  }

  /// Clear authentication state
  Future<void> clearAuthState() async {
    try {
      await _secureStorage.delete(key: _authStateKey);
      _logger.i('Auth state cleared successfully');
    } catch (e) {
      _logger.e('Failed to clear auth state: $e');
    }
  }

  /// Save user token (if needed for API calls)
  Future<void> saveUserToken(String token) async {
    try {
      await _secureStorage.write(key: _userTokenKey, value: token);
      _logger.i('User token saved successfully');
    } catch (e) {
      _logger.e('Failed to save user token: $e');
    }
  }

  /// Get saved user token
  Future<String?> getUserToken() async {
    try {
      final token = await _secureStorage.read(key: _userTokenKey);
      if (token != null) {
        _logger.i('User token retrieved successfully');
      } else {
        _logger.i('No user token found in local storage');
      }
      return token;
    } catch (e) {
      _logger.e('Failed to retrieve user token: $e');
      return null;
    }
  }

  /// Clear user token
  Future<void> clearUserToken() async {
    try {
      await _secureStorage.delete(key: _userTokenKey);
      _logger.i('User token cleared successfully');
    } catch (e) {
      _logger.e('Failed to clear user token: $e');
    }
  }

  /// Check if this is the first app launch
  bool get isFirstLaunch {
    return _prefs?.getBool(_isFirstLaunchKey) ?? true;
  }

  /// Set first launch flag
  Future<void> setFirstLaunchComplete() async {
    try {
      await _prefs?.setBool(_isFirstLaunchKey, false);
      _logger.i('First launch flag set to false');
    } catch (e) {
      _logger.e('Failed to set first launch flag: $e');
    }
  }

  /// Save last login time
  Future<void> saveLastLoginTime(DateTime loginTime) async {
    try {
      await _prefs?.setString(_lastLoginTimeKey, loginTime.toIso8601String());
      _logger.i('Last login time saved: $loginTime');
    } catch (e) {
      _logger.e('Failed to save last login time: $e');
    }
  }

  /// Get last login time
  DateTime? get lastLoginTime {
    try {
      final timeString = _prefs?.getString(_lastLoginTimeKey);
      if (timeString != null) {
        return DateTime.parse(timeString);
      }
      return null;
    } catch (e) {
      _logger.e('Failed to retrieve last login time: $e');
      return null;
    }
  }

  /// Save app version
  Future<void> saveAppVersion(String version) async {
    try {
      await _prefs?.setString(_appVersionKey, version);
      _logger.i('App version saved: $version');
    } catch (e) {
      _logger.e('Failed to save app version: $e');
    }
  }

  /// Get saved app version
  String? get appVersion {
    return _prefs?.getString(_appVersionKey);
  }

  /// Save user preferences
  Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    try {
      final preferencesJson = jsonEncode(preferences);
      await _prefs?.setString(_userPreferencesKey, preferencesJson);
      _logger.i('User preferences saved successfully');
    } catch (e) {
      _logger.e('Failed to save user preferences: $e');
    }
  }

  /// Get user preferences
  Map<String, dynamic>? get userPreferences {
    try {
      final preferencesJson = _prefs?.getString(_userPreferencesKey);
      if (preferencesJson != null) {
        return jsonDecode(preferencesJson) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      _logger.e('Failed to retrieve user preferences: $e');
      return null;
    }
  }

  /// Clear all user-related data (for logout)
  Future<void> clearAllUserData() async {
    try {
      _logger.i('Clearing all user data from local storage');
      
      // Clear secure storage
      await clearUserData();
      await clearAuthState();
      await clearUserToken();
      
      // Clear shared preferences user data
      await _prefs?.remove(_lastLoginTimeKey);
      await _prefs?.remove(_userPreferencesKey);
      
      _logger.i('All user data cleared successfully');
    } catch (e) {
      _logger.e('Failed to clear all user data: $e');
    }
  }

  /// Clear all app data (for app reset)
  Future<void> clearAllData() async {
    try {
      _logger.i('Clearing all app data from local storage');
      
      // Clear all secure storage
      await _secureStorage.deleteAll();
      
      // Clear all shared preferences
      await _prefs?.clear();
      
      _logger.i('All app data cleared successfully');
    } catch (e) {
      _logger.e('Failed to clear all app data: $e');
    }
  }
}
