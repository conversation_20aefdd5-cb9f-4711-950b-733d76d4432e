
import 'package:firebase_analytics/firebase_analytics.dart';

class AnalyticsService {
  static final FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  static FirebaseAnalyticsObserver getObserver() {
    return FirebaseAnalyticsObserver(analytics: analytics);
  }

  static Future<void> logScreenView(String screenName) async {
    await analytics.logScreenView(screenName: screenName);
  }

  static Future<void> logCustomEvent(
    String name, {
    Map<String, Object>? params,
  }) async {
    await analytics.logEvent(name: name, parameters: params);
  }
}
