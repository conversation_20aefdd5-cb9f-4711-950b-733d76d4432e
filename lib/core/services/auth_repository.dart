import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';

import '../enums/auth_enums.dart';
import '../models/auth_error_model.dart';
import '../models/user_model.dart';

/// Repository for managing authentication with Firebase Auth
class AuthRepository {
  static final AuthRepository _instance = AuthRepository._internal();
  factory AuthRepository() => _instance;
  AuthRepository._internal();

  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final Logger _logger = Logger();

  /// Stream of authentication state changes
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  /// Stream of user changes (includes profile updates)
  Stream<User?> get userChanges => _firebaseAuth.userChanges();

  /// Get current Firebase user
  User? get currentFirebaseUser => _firebaseAuth.currentUser;

  /// Get current user as UserModel
  UserModel? get currentUser {
    final firebaseUser = currentFirebaseUser;
    if (firebaseUser == null) return null;
    return UserModel.fromFirebaseUser(firebaseUser);
  }

  /// Sign in with email and password
  Future<UserModel> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _logger.i('Attempting to sign in user with email: $email');
      
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user == null) {
        _logger.e('Sign in failed: No user returned from Firebase');
        throw AuthErrorModel.custom(
          type: AuthErrorType.unknown,
          customMessage: 'Sign in failed. Please try again.',
          operation: AuthOperation.signIn,
        );
      }

      final user = UserModel.fromFirebaseUser(credential.user!);
      _logger.i('Successfully signed in user: ${user.email}');
      
      return user;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during sign in: ${e.code} - ${e.message}');
      throw AuthErrorModel.fromFirebaseException(e, operation: AuthOperation.signIn);
    } catch (e) {
      _logger.e('Unexpected error during sign in: $e');
      throw AuthErrorModel.fromException(
        Exception(e.toString()),
        operation: AuthOperation.signIn,
      );
    }
  }

  /// Sign up with email and password
  Future<UserModel> signUpWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      _logger.i('Attempting to sign up user with email: $email');
      
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user == null) {
        _logger.e('Sign up failed: No user returned from Firebase');
        throw AuthErrorModel.custom(
          type: AuthErrorType.unknown,
          customMessage: 'Sign up failed. Please try again.',
          operation: AuthOperation.signUp,
        );
      }

      // Update display name if provided
      if (displayName != null && displayName.isNotEmpty) {
        await credential.user!.updateDisplayName(displayName.trim());
        await credential.user!.reload();
      }

      final user = UserModel.fromFirebaseUser(credential.user!);
      _logger.i('Successfully signed up user: ${user.email}');
      
      return user;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during sign up: ${e.code} - ${e.message}');
      throw AuthErrorModel.fromFirebaseException(e, operation: AuthOperation.signUp);
    } catch (e) {
      _logger.e('Unexpected error during sign up: $e');
      throw AuthErrorModel.fromException(
        Exception(e.toString()),
        operation: AuthOperation.signUp,
      );
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    try {
      _logger.i('Attempting to sign out current user');
      await _firebaseAuth.signOut();
      _logger.i('Successfully signed out user');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during sign out: ${e.code} - ${e.message}');
      throw AuthErrorModel.fromFirebaseException(e, operation: AuthOperation.signOut);
    } catch (e) {
      _logger.e('Unexpected error during sign out: $e');
      throw AuthErrorModel.fromException(
        Exception(e.toString()),
        operation: AuthOperation.signOut,
      );
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail({required String email}) async {
    try {
      _logger.i('Sending password reset email to: $email');
      await _firebaseAuth.sendPasswordResetEmail(email: email.trim());
      _logger.i('Successfully sent password reset email');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during password reset: ${e.code} - ${e.message}');
      throw AuthErrorModel.fromFirebaseException(e, operation: AuthOperation.resetPassword);
    } catch (e) {
      _logger.e('Unexpected error during password reset: $e');
      throw AuthErrorModel.fromException(
        Exception(e.toString()),
        operation: AuthOperation.resetPassword,
      );
    }
  }

  /// Send email verification
  Future<void> sendEmailVerification() async {
    try {
      final user = currentFirebaseUser;
      if (user == null) {
        throw AuthErrorModel.custom(
          type: AuthErrorType.userNotFound,
          customMessage: 'No user is currently signed in.',
          operation: AuthOperation.verifyEmail,
        );
      }

      if (user.emailVerified) {
        _logger.i('Email is already verified for user: ${user.email}');
        return;
      }

      _logger.i('Sending email verification to: ${user.email}');
      await user.sendEmailVerification();
      _logger.i('Successfully sent email verification');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during email verification: ${e.code} - ${e.message}');
      throw AuthErrorModel.fromFirebaseException(e, operation: AuthOperation.verifyEmail);
    } catch (e) {
      _logger.e('Unexpected error during email verification: $e');
      throw AuthErrorModel.fromException(
        Exception(e.toString()),
        operation: AuthOperation.verifyEmail,
      );
    }
  }

  /// Update user profile
  Future<UserModel> updateProfile({
    String? displayName,
    String? photoUrl,
  }) async {
    try {
      final user = currentFirebaseUser;
      if (user == null) {
        throw AuthErrorModel.custom(
          type: AuthErrorType.userNotFound,
          customMessage: 'No user is currently signed in.',
          operation: AuthOperation.updateProfile,
        );
      }

      _logger.i('Updating profile for user: ${user.email}');
      
      if (displayName != null) {
        await user.updateDisplayName(displayName.trim());
      }
      
      if (photoUrl != null) {
        await user.updatePhotoURL(photoUrl.trim());
      }

      await user.reload();
      final updatedUser = UserModel.fromFirebaseUser(_firebaseAuth.currentUser!);
      
      _logger.i('Successfully updated profile for user: ${updatedUser.email}');
      return updatedUser;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during profile update: ${e.code} - ${e.message}');
      throw AuthErrorModel.fromFirebaseException(e, operation: AuthOperation.updateProfile);
    } catch (e) {
      _logger.e('Unexpected error during profile update: $e');
      throw AuthErrorModel.fromException(
        Exception(e.toString()),
        operation: AuthOperation.updateProfile,
      );
    }
  }

  /// Update user password
  Future<void> updatePassword({required String newPassword}) async {
    try {
      final user = currentFirebaseUser;
      if (user == null) {
        throw AuthErrorModel.custom(
          type: AuthErrorType.userNotFound,
          customMessage: 'No user is currently signed in.',
          operation: AuthOperation.updatePassword,
        );
      }

      _logger.i('Updating password for user: ${user.email}');
      await user.updatePassword(newPassword);
      _logger.i('Successfully updated password');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during password update: ${e.code} - ${e.message}');
      throw AuthErrorModel.fromFirebaseException(e, operation: AuthOperation.updatePassword);
    } catch (e) {
      _logger.e('Unexpected error during password update: $e');
      throw AuthErrorModel.fromException(
        Exception(e.toString()),
        operation: AuthOperation.updatePassword,
      );
    }
  }

  /// Delete user account
  Future<void> deleteAccount() async {
    try {
      final user = currentFirebaseUser;
      if (user == null) {
        throw AuthErrorModel.custom(
          type: AuthErrorType.userNotFound,
          customMessage: 'No user is currently signed in.',
          operation: AuthOperation.deleteAccount,
        );
      }

      _logger.i('Deleting account for user: ${user.email}');
      await user.delete();
      _logger.i('Successfully deleted account');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during account deletion: ${e.code} - ${e.message}');
      throw AuthErrorModel.fromFirebaseException(e, operation: AuthOperation.deleteAccount);
    } catch (e) {
      _logger.e('Unexpected error during account deletion: $e');
      throw AuthErrorModel.fromException(
        Exception(e.toString()),
        operation: AuthOperation.deleteAccount,
      );
    }
  }

  /// Reload current user data
  Future<UserModel?> reloadUser() async {
    try {
      final user = currentFirebaseUser;
      if (user == null) return null;

      _logger.i('Reloading user data for: ${user.email}');
      await user.reload();
      final reloadedUser = UserModel.fromFirebaseUser(_firebaseAuth.currentUser!);
      _logger.i('Successfully reloaded user data');
      
      return reloadedUser;
    } catch (e) {
      _logger.e('Error reloading user data: $e');
      return null;
    }
  }
}
