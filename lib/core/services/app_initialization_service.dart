import 'package:logger/logger.dart';

import '../models/user_model.dart';

import 'local_storage_service.dart';
import 'user_data_service.dart';

/// Service for handling app initialization tasks
class AppInitializationService {
  static final AppInitializationService _instance =
      AppInitializationService._internal();
  factory AppInitializationService() => _instance;
  AppInitializationService._internal();

  final Logger _logger = Logger();
  final LocalStorageService _localStorageService = LocalStorageService();
  final UserDataService _userDataService = UserDataService();

  bool _isInitialized = false;

  /// Initialize the app services
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.i('App already initialized');
      return;
    }

    try {
      _logger.i('Starting app initialization');

      // Initialize local storage service
      await _localStorageService.initialize();
      _logger.i('Local storage service initialized');

      // Check if this is the first launch
      if (_localStorageService.isFirstLaunch) {
        _logger.i('First app launch detected');
        await _handleFirstLaunch();
      }

      _isInitialized = true;
      _logger.i('App initialization completed successfully');
    } catch (e) {
      _logger.e('Failed to initialize app: $e');
      rethrow;
    }
  }

  /// Handle first app launch
  Future<void> _handleFirstLaunch() async {
    try {
      _logger.i('Handling first app launch');

      // Clear any existing data (just in case)
      await _localStorageService.clearAllData();

      // Set first launch flag
      await _localStorageService.setFirstLaunchComplete();

      _logger.i('First launch setup completed');
    } catch (e) {
      _logger.e('Failed to handle first launch: $e');
    }
  }

  /// Load user data after authentication
  Future<void> loadUserData(UserModel user) async {
    try {
      _logger.i('Loading user data for: ${user.email}');

      // Save user data locally
      await _localStorageService.saveUserData(user);

      // Load all user data
      final userData = await _userDataService.loadAllUserData(user);

      // Save user data locally
      await _userDataService.saveUserDataLocally(userData);

      // Sync user data (dummy implementation for now)
      await _userDataService.syncUserData(user);

      _logger.i('User data loaded successfully');
    } catch (e) {
      _logger.e('Failed to load user data: $e');
      // Don't rethrow here as this shouldn't block authentication
    }
  }

  /// Clear user data on logout
  Future<void> clearUserData() async {
    try {
      _logger.i('Clearing user data');
      await _userDataService.clearUserData();
      _logger.i('User data cleared successfully');
    } catch (e) {
      _logger.e('Failed to clear user data: $e');
    }
  }

  /// Check if app is initialized
  bool get isInitialized => _isInitialized;

  /// Get saved user data
  Future<UserModel?> getSavedUserData() async {
    try {
      return await _localStorageService.getUserData();
    } catch (e) {
      _logger.e('Failed to get saved user data: $e');
      return null;
    }
  }
}
