import 'package:logger/logger.dart';

import '../models/user_model.dart';
import '../enums/auth_enums.dart';
import '../../features/profile/repositories/profile_repository.dart';
import '../../features/profile/models/profile_model.dart';
import 'local_storage_service.dart';

/// Service for managing user data loading and synchronization
class UserDataService {
  static final UserDataService _instance = UserDataService._internal();
  factory UserDataService() => _instance;
  UserDataService._internal();

  final Logger _logger = Logger();
  final LocalStorageService _localStorageService = LocalStorageService();
  final ProfileRepository _profileRepository = ProfileRepository();

  /// Load all user data on app startup or login
  /// This is a dummy implementation that can be expanded later
  Future<Map<String, dynamic>> loadAllUserData(UserModel user) async {
    try {
      _logger.i('Loading all user data for user: ${user.email}');

      // Initialize the data map
      final userData = <String, dynamic>{};

      // Load basic user information
      userData['user'] = user.toJson();

      // Load user preferences from local storage
      final preferences = _localStorageService.userPreferences;
      userData['preferences'] = preferences ?? _getDefaultPreferences();

      // Load last login time
      final lastLoginTime = _localStorageService.lastLoginTime;
      userData['lastLoginTime'] = lastLoginTime?.toIso8601String();

      // Load user profile data from Firestore
      userData['profile'] = await _loadUserProfile(user);
      _logger.i('Loaded user profile: ${userData['profile']}');

      // Ensure user has a profile in Firestore (create if doesn't exist)
      await createOrUpdateUserProfile(user);

      _logger.i('Successfully loaded all user data');
      return userData;
    } catch (e) {
      _logger.e('Failed to load user data: $e');
      throw Exception('Failed to load user data: $e');
    }
  }

  /// Load user profile data from Firestore
  Future<Map<String, dynamic>> _loadUserProfile(UserModel user) async {
    try {
      _logger.i('Loading user profile for: ${user.email}');

      // Try to load profile from Firestore
      final profile = await _profileRepository.getProfileById(user.id);

      if (profile != null) {
        _logger.i('Found existing profile for user: ${profile.fullName}');
        return profile.toJson();
      } else {
        _logger.i('No profile found, creating basic profile from user data');

        // Create a basic profile from user data if none exists
        final basicProfile = ProfileModel(
          id: user.id,
          fullName: user.fullName ?? user.displayName ?? 'User',
          email: user.email,
          userType: user.userType,
          profileImageUrl: user.profileImageUrl,
          grade: user.grade,
          school: user.school,
          studentId: user.studentId,
          bio: user.bio,
          subjects: user.subjects,
          phoneNumber: user.phoneNumber,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Save the basic profile to Firestore for future use
        try {
          final createdProfile = await _profileRepository.createProfile(
            basicProfile,
          );
          _logger.i('Created new profile for user: ${createdProfile.fullName}');
          return createdProfile.toJson();
        } catch (createError) {
          _logger.w(
            'Failed to create profile in Firestore, using basic data: $createError',
          );
          return basicProfile.toJson();
        }
      }
    } catch (e) {
      _logger.e('Failed to load user profile: $e');

      // Return basic profile data as fallback
      return {
        'id': user.id,
        'fullName': user.fullName ?? user.displayName ?? 'User',
        'email': user.email,
        'userType': user.userType.value,
        'profileImageUrl': user.profileImageUrl,
        'grade': user.grade,
        'school': user.school,
        'studentId': user.studentId,
        'bio': user.bio,
        'subjects': user.subjects,
        'phoneNumber': user.phoneNumber,
        'createdAt': user.createdAt?.toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
        'isActive': true,
      };
    }
  }

  /// Get default user preferences
  Map<String, dynamic> _getDefaultPreferences() {
    return {
      'theme': 'system',
      'language': 'en',
      'firstTimeUser': true,
      'onboardingCompleted': false,
    };
  }

  /// Save user data to local storage
  Future<void> saveUserDataLocally(Map<String, dynamic> userData) async {
    try {
      _logger.i('Saving user data to local storage');

      // Save user preferences
      if (userData['preferences'] != null) {
        await _localStorageService.saveUserPreferences(
          userData['preferences'] as Map<String, dynamic>,
        );
      }

      // Save last login time
      await _localStorageService.saveLastLoginTime(DateTime.now());

      _logger.i('User data saved to local storage successfully');
    } catch (e) {
      _logger.e('Failed to save user data locally: $e');
    }
  }

  /// Sync user data with remote server (dummy implementation)
  Future<void> syncUserData(UserModel user) async {
    try {
      _logger.i('Syncing user data for: ${user.email}');

      // TODO: Implement actual sync logic with Firestore
      // This would include:
      // - Uploading local changes to Firestore
      // - Downloading remote changes from Firestore
      // - Resolving conflicts
      // - Updating local storage

      _logger.i('User data sync completed (dummy implementation)');
    } catch (e) {
      _logger.e('Failed to sync user data: $e');
    }
  }

  /// Create or update user profile
  Future<ProfileModel?> createOrUpdateUserProfile(UserModel user) async {
    try {
      _logger.i('Creating or updating profile for user: ${user.email}');

      // Check if profile already exists
      final existingProfile = await _profileRepository.getProfileById(user.id);

      if (existingProfile != null) {
        _logger.i(
          'Profile already exists for user: ${existingProfile.fullName}',
        );
        return existingProfile;
      } else {
        // Create new profile from user data
        final newProfile = ProfileModel(
          id: user.id,
          fullName: user.fullName ?? user.displayName ?? 'User',
          email: user.email,
          userType: user.userType,
          profileImageUrl: user.profileImageUrl,
          grade: user.grade,
          school: user.school,
          studentId: user.studentId,
          bio: user.bio,
          subjects: user.subjects,
          phoneNumber: user.phoneNumber,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final createdProfile = await _profileRepository.createProfile(
          newProfile,
        );
        _logger.i('Created new profile for user: ${createdProfile.fullName}');
        return createdProfile;
      }
    } catch (e) {
      _logger.e('Failed to create or update user profile: $e');
      return null;
    }
  }

  /// Clear all user data (for logout)
  Future<void> clearUserData() async {
    try {
      _logger.i('Clearing all user data');
      await _localStorageService.clearAllUserData();
      _logger.i('User data cleared successfully');
    } catch (e) {
      _logger.e('Failed to clear user data: $e');
    }
  }
}
