import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

/// Service class to handle file picking and camera operations
class FilePickerService {
  static final FilePickerService _instance = FilePickerService._internal();
  factory FilePickerService() => _instance;
  FilePickerService._internal();

  final ImagePicker _imagePicker = ImagePicker();

  /// Pick files from device storage
  Future<List<File>?> pickFiles({
    FileType type = FileType.any,
    List<String>? allowedExtensions,
    bool allowMultiple = true,
  }) async {
    try {
      // Request storage permission
      final storagePermission = await _requestStoragePermission();
      if (!storagePermission) {
        debugPrint('Storage permission denied');
        return null;
      }

      final result = await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: allowedExtensions,
        allowMultiple: allowMultiple,
        withData: false, // Don't load file data into memory
        withReadStream: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final files = result.files
            .where((file) => file.path != null)
            .map((file) => File(file.path!))
            .toList();
        
        debugPrint('Picked ${files.length} files');
        return files;
      }
      
      return null;
    } catch (e) {
      debugPrint('Error picking files: $e');
      return null;
    }
  }

  /// Pick documents (PDF, DOC, DOCX, TXT)
  Future<List<File>?> pickDocuments({bool allowMultiple = true}) async {
    return await pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf', 'doc', 'docx', 'txt', 'rtf'],
      allowMultiple: allowMultiple,
    );
  }

  /// Pick images from gallery
  Future<List<File>?> pickImages({bool allowMultiple = true}) async {
    try {
      // Request photo permission
      final photoPermission = await _requestPhotoPermission();
      if (!photoPermission) {
        debugPrint('Photo permission denied');
        return null;
      }

      if (allowMultiple) {
        final images = await _imagePicker.pickMultipleMedia();
        if (images.isNotEmpty) {
          final files = images.map((image) => File(image.path)).toList();
          debugPrint('Picked ${files.length} images');
          return files;
        }
      } else {
        final image = await _imagePicker.pickImage(source: ImageSource.gallery);
        if (image != null) {
          debugPrint('Picked 1 image');
          return [File(image.path)];
        }
      }
      
      return null;
    } catch (e) {
      debugPrint('Error picking images: $e');
      return null;
    }
  }

  /// Capture photo from camera
  Future<File?> capturePhoto() async {
    try {
      // Request camera permission
      final cameraPermission = await _requestCameraPermission();
      if (!cameraPermission) {
        debugPrint('Camera permission denied');
        return null;
      }

      final image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85, // Compress image to reduce file size
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        debugPrint('Captured photo: ${image.path}');
        return File(image.path);
      }
      
      return null;
    } catch (e) {
      debugPrint('Error capturing photo: $e');
      return null;
    }
  }

  /// Capture video from camera
  Future<File?> captureVideo() async {
    try {
      // Request camera permission
      final cameraPermission = await _requestCameraPermission();
      if (!cameraPermission) {
        debugPrint('Camera permission denied');
        return null;
      }

      final video = await _imagePicker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(minutes: 5), // Limit video duration
      );

      if (video != null) {
        debugPrint('Captured video: ${video.path}');
        return File(video.path);
      }
      
      return null;
    } catch (e) {
      debugPrint('Error capturing video: $e');
      return null;
    }
  }

  /// Request storage permission
  Future<bool> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      // For Android 13+ (API 33+), we need different permissions
      final androidInfo = await _getAndroidVersion();
      if (androidInfo >= 33) {
        // Android 13+ uses scoped storage, no special permission needed for file picker
        return true;
      } else {
        // Android 12 and below
        final status = await Permission.storage.request();
        return status.isGranted;
      }
    } else if (Platform.isIOS) {
      // iOS doesn't need storage permission for file picker
      return true;
    }
    return true;
  }

  /// Request camera permission
  Future<bool> _requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  /// Request photo permission (iOS specific)
  Future<bool> _requestPhotoPermission() async {
    if (Platform.isIOS) {
      final status = await Permission.photos.request();
      return status.isGranted;
    } else {
      // Android uses storage permission for photos
      return await _requestStoragePermission();
    }
  }

  /// Get Android SDK version
  Future<int> _getAndroidVersion() async {
    if (Platform.isAndroid) {
      // This is a simplified version - in a real app you might want to use
      // device_info_plus package for more accurate version detection
      return 33; // Assume modern Android for now
    }
    return 0;
  }

  /// Get file size in a human-readable format
  String getFileSize(File file) {
    final bytes = file.lengthSync();
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Get file extension from file path
  String getFileExtension(String filePath) {
    return filePath.split('.').last.toLowerCase();
  }

  /// Check if file is an image
  bool isImageFile(String filePath) {
    final extension = getFileExtension(filePath);
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  /// Check if file is a video
  bool isVideoFile(String filePath) {
    final extension = getFileExtension(filePath);
    return ['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', '3gp'].contains(extension);
  }

  /// Check if file is a document
  bool isDocumentFile(String filePath) {
    final extension = getFileExtension(filePath);
    return ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].contains(extension);
  }
}
