import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:logger/logger.dart';

import '../providers/auth_providers.dart';
import '../providers/user_preferences_provider.dart';

/// Service for collecting debug information and managing logs
class DebugService {
  static final DebugService _instance = DebugService._internal();
  factory DebugService() => _instance;
  DebugService._internal();

  final Logger _logger = Logger();
  final List<DebugLogEntry> _logs = [];
  final int _maxLogs = 1000;

  /// Add a log entry
  void addLog(DebugLogEntry entry) {
    _logs.insert(0, entry);
    if (_logs.length > _maxLogs) {
      _logs.removeRange(_maxLogs, _logs.length);
    }
  }

  /// Get all logs
  List<DebugLogEntry> get logs => List.unmodifiable(_logs);

  /// Clear all logs
  void clearLogs() {
    _logs.clear();
  }

  /// Get logs filtered by type
  List<DebugLogEntry> getLogsByType(DebugLogType type) {
    return _logs.where((log) => log.type == type).toList();
  }

  /// Export logs as JSON string
  String exportLogs() {
    final logsJson = _logs.map((log) => log.toJson()).toList();
    return const JsonEncoder.withIndent('  ').convert(logsJson);
  }

  /// Get device information
  Future<Map<String, dynamic>> getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final packageInfo = await PackageInfo.fromPlatform();

      Map<String, dynamic> info = {
        'platform': Platform.operatingSystem,
        'platformVersion': Platform.operatingSystemVersion,
        'appName': packageInfo.appName,
        'packageName': packageInfo.packageName,
        'version': packageInfo.version,
        'buildNumber': packageInfo.buildNumber,
        'isPhysicalDevice': true,
        'screenSize': 'Unknown',
      };

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        info.addAll({
          'deviceModel': androidInfo.model,
          'deviceBrand': androidInfo.brand,
          'deviceManufacturer': androidInfo.manufacturer,
          'androidVersion': androidInfo.version.release,
          'androidSdkInt': androidInfo.version.sdkInt,
          'isPhysicalDevice': androidInfo.isPhysicalDevice,
        });
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        info.addAll({
          'deviceModel': iosInfo.model,
          'deviceName': iosInfo.name,
          'systemName': iosInfo.systemName,
          'systemVersion': iosInfo.systemVersion,
          'isPhysicalDevice': iosInfo.isPhysicalDevice,
        });
      }

      return info;
    } catch (e) {
      _logger.e('Error getting device info: $e');
      return {'error': e.toString()};
    }
  }

  /// Get network connectivity status
  Future<Map<String, dynamic>> getConnectivityInfo() async {
    try {
      final connectivity = Connectivity();
      final connectivityResults = await connectivity.checkConnectivity();

      return {
        'status': connectivityResults.toString(),
        'isConnected': !connectivityResults.contains(ConnectivityResult.none),
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      _logger.e('Error getting connectivity info: $e');
      return {'error': e.toString()};
    }
  }

  /// Get Firebase configuration info
  Map<String, dynamic> getFirebaseInfo() {
    try {
      final app = Firebase.app();
      return {
        'projectId': app.options.projectId,
        'appId': app.options.appId,
        'apiKey': '${app.options.apiKey.substring(0, 10)}...',
        'messagingSenderId': app.options.messagingSenderId,
        'storageBucket': app.options.storageBucket,
        'isInitialized': true,
      };
    } catch (e) {
      return {'isInitialized': false, 'error': e.toString()};
    }
  }

  /// Get Firebase Auth status
  Map<String, dynamic> getFirebaseAuthStatus() {
    try {
      final auth = FirebaseAuth.instance;
      final currentUser = auth.currentUser;

      return {
        'isSignedIn': currentUser != null,
        'userId': currentUser?.uid,
        'email': currentUser?.email,
        'emailVerified': currentUser?.emailVerified,
        'displayName': currentUser?.displayName,
        'photoURL': currentUser?.photoURL,
        'lastSignInTime': currentUser?.metadata.lastSignInTime
            ?.toIso8601String(),
        'creationTime': currentUser?.metadata.creationTime?.toIso8601String(),
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  /// Get Firestore connection status
  Future<Map<String, dynamic>> getFirestoreStatus() async {
    try {
      final firestore = FirebaseFirestore.instance;

      // Try to perform a simple operation to test connectivity
      await firestore.enableNetwork();

      return {
        'isConnected': true,
        'host': firestore.settings.host,
        'persistenceEnabled': firestore.settings.persistenceEnabled,
        'cacheSizeBytes': firestore.settings.cacheSizeBytes,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {'isConnected': false, 'error': e.toString()};
    }
  }

  /// Get provider states information
  Map<String, dynamic> getProviderStates(WidgetRef ref) {
    try {
      final authState = ref.read(authStateProvider);
      final userPreferences = ref.read(userPreferencesProvider);
      final currentUser = ref.read(currentUserProvider);

      return {
        'authState': {
          'hasValue': authState.hasValue,
          'hasError': authState.hasError,
          'isLoading': authState.isLoading,
          'value': authState.hasValue
              ? {
                  'isAuthenticated': authState.value?.isAuthenticated,
                  'isLoading': authState.value?.isLoading,
                  'hasError': authState.value?.error != null,
                  'error': authState.value?.error?.toString(),
                }
              : null,
        },
        'userPreferences': {'themeMode': userPreferences.name},
        'currentUser': currentUser != null
            ? {
                'id': currentUser.id,
                'email': currentUser.email,
                'displayName': currentUser.displayName,
                'emailVerified': currentUser.emailVerified,
              }
            : null,
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  /// Get local storage information
  Future<Map<String, dynamic>> getLocalStorageInfo() async {
    try {
      // This would require access to the local storage service
      // For now, return basic info
      return {
        'hasUserData': 'Unknown',
        'lastLoginTime': 'Unknown',
        'isFirstLaunch': 'Unknown',
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  /// Get app state information
  Map<String, dynamic> getAppStateInfo(BuildContext context) {
    try {
      final mediaQuery = MediaQuery.of(context);
      final theme = Theme.of(context);

      return {
        'screenSize': {
          'width': mediaQuery.size.width,
          'height': mediaQuery.size.height,
          'devicePixelRatio': mediaQuery.devicePixelRatio,
        },
        'theme': {
          'brightness': theme.brightness.name,
          'primaryColor': theme.primaryColor.toString(),
          'useMaterial3': theme.useMaterial3,
        },
        'platform': {
          'isAndroid': Platform.isAndroid,
          'isIOS': Platform.isIOS,
          'isMacOS': Platform.isMacOS,
          'isWindows': Platform.isWindows,
          'isLinux': Platform.isLinux,
          'isWeb': kIsWeb,
        },
        'debugMode': kDebugMode,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  /// Log a debug message
  void logDebug(
    String message, {
    String? source,
    Map<String, dynamic>? metadata,
  }) {
    final entry = DebugLogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      type: DebugLogType.debug,
      message: message,
      source: source,
      metadata: metadata,
    );
    addLog(entry);
  }

  /// Log an info message
  void logInfo(
    String message, {
    String? source,
    Map<String, dynamic>? metadata,
  }) {
    final entry = DebugLogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      type: DebugLogType.info,
      message: message,
      source: source,
      metadata: metadata,
    );
    addLog(entry);
  }

  /// Log a warning message
  void logWarning(
    String message, {
    String? source,
    Map<String, dynamic>? metadata,
  }) {
    final entry = DebugLogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      type: DebugLogType.warning,
      message: message,
      source: source,
      metadata: metadata,
    );
    addLog(entry);
  }

  /// Log an error message
  void logError(
    String message, {
    String? source,
    Map<String, dynamic>? metadata,
  }) {
    final entry = DebugLogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      type: DebugLogType.error,
      message: message,
      source: source,
      metadata: metadata,
    );
    addLog(entry);
  }

  /// Log a network operation
  void logNetwork(
    String message, {
    String? source,
    Map<String, dynamic>? metadata,
  }) {
    final entry = DebugLogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      type: DebugLogType.network,
      message: message,
      source: source,
      metadata: metadata,
    );
    addLog(entry);
  }

  /// Log a Firebase operation
  void logFirebase(
    String message, {
    String? source,
    Map<String, dynamic>? metadata,
  }) {
    final entry = DebugLogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      type: DebugLogType.firebase,
      message: message,
      source: source,
      metadata: metadata,
    );
    addLog(entry);
  }

  /// Log an auth operation
  void logAuth(
    String message, {
    String? source,
    Map<String, dynamic>? metadata,
  }) {
    final entry = DebugLogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      type: DebugLogType.auth,
      message: message,
      source: source,
      metadata: metadata,
    );
    addLog(entry);
  }

  /// Log a navigation operation
  void logNavigation(
    String message, {
    String? source,
    Map<String, dynamic>? metadata,
  }) {
    final entry = DebugLogEntry(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      timestamp: DateTime.now(),
      type: DebugLogType.navigation,
      message: message,
      source: source,
      metadata: metadata,
    );
    addLog(entry);
  }
}

/// Debug log entry model
class DebugLogEntry {
  final String id;
  final DateTime timestamp;
  final DebugLogType type;
  final String message;
  final String? source;
  final Map<String, dynamic>? metadata;

  DebugLogEntry({
    required this.id,
    required this.timestamp,
    required this.type,
    required this.message,
    this.source,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'type': type.name,
      'message': message,
      'source': source,
      'metadata': metadata,
    };
  }

  factory DebugLogEntry.fromJson(Map<String, dynamic> json) {
    return DebugLogEntry(
      id: json['id'],
      timestamp: DateTime.parse(json['timestamp']),
      type: DebugLogType.values.firstWhere((e) => e.name == json['type']),
      message: json['message'],
      source: json['source'],
      metadata: json['metadata'],
    );
  }
}

/// Debug log types
enum DebugLogType {
  debug,
  info,
  warning,
  error,
  network,
  firebase,
  auth,
  navigation,
}

/// Provider for debug service
final debugServiceProvider = Provider<DebugService>((ref) {
  return DebugService();
});
