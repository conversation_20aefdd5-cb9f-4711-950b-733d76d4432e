{"indexes": [{"collectionGroup": "classrooms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "classrooms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "studentIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "classrooms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "teacherId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "classrooms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "classrooms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "studentIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "isPrimaryClass", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "activities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classroomId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "activities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classroomId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "activities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classroomId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}